# كيفية تحميل إضافة AI SEO Optimizer في Chrome

## الخطوات:

### 1. فتح Chrome Extensions
- افتح متصفح Chrome
- اذهب إلى `chrome://extensions/` في شريط العناوين
- أو اذهب إلى القائمة ← More Tools ← Extensions

### 2. تفعيل Developer Mode
- في الزاوية العلوية اليمنى، فعّل "Developer mode"

### 3. تحميل الإضافة
- انقر على "Load unpacked"
- اختر مجلد `Ai SEO Optimizer` الكامل
- انقر "Select Folder"

### 4. التحقق من التحميل
- يجب أن تظهر الإضافة في قائمة Extensions
- تأكد من أن الإضافة مفعلة (enabled)

### 5. اختبار الإضافة
- افتح الملف `test-extension.html` في متصفح Chrome
- انقر على أيقونة الإضافة في شريط الأدوات
- جرب الميزات المختلفة:
  - انقر "Analyze Page" لتحليل الصفحة
  - انقر "Keywords" لتوليد الكلمات المفتاحية
  - انقر "Speed Test" لاختبار السرعة
  - جرب تبديل التبويبات (Dashboard, Analysis, Settings)

## استكشاف الأخطاء:

### إذا لم تظهر الإضافة:
1. تأكد من أن جميع الملفات موجودة في المجلد
2. تحقق من ملف `manifest.json`
3. افتح Developer Tools (F12) وتحقق من Console للأخطاء

### إذا لم تعمل الميزات:
1. افتح Developer Tools
2. اذهب إلى Console tab
3. ابحث عن رسائل الخطأ
4. تأكد من أن Background Script يعمل

### إذا لم تظهر النافذة المنبثقة:
1. تأكد من أن ملف `popup.html` موجود
2. تحقق من مسار الملفات في `manifest.json`
3. تأكد من أن CSS و JS محملين بشكل صحيح

## الملفات المطلوبة:
- `manifest.json` - إعدادات الإضافة
- `popup/popup.html` - واجهة المستخدم
- `popup/popup-simple.js` - منطق الواجهة
- `popup/popup-modern.css` - تصميم الواجهة
- `background/background-minimal.js` - خدمة الخلفية
- `content/content-simple.js` - سكريبت المحتوى
- `assets/icons/` - أيقونات الإضافة

## نصائح:
- استخدم `test-extension.html` لاختبار الإضافة
- راقب Console للرسائل والأخطاء
- جرب الإضافة على مواقع مختلفة
- تأكد من أن جميع الميزات تعمل قبل النشر

## إذا واجهت مشاكل:
1. تحقق من Console في Developer Tools
2. تأكد من أن جميع الملفات محفوظة
3. أعد تحميل الإضافة (Reload button في Extensions page)
4. جرب إعادة تشغيل Chrome
