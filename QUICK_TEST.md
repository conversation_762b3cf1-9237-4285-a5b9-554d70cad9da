# اختبار سريع لإصلاح مشكلة التبويبات

## 🚀 الخطوات السريعة

### 1. إعادة تحميل الإضافة
```
1. اذهب إلى chrome://extensions/
2. ابحث عن "AI SEO Optimizer"
3. انقر على زر "Reload" 🔄
```

### 2. اختبار التبويبات
```
1. افتح أي صفحة ويب (أو test-tabs.html)
2. انقر على أيقونة الإضافة
3. جرب النقر على كل تبويب:
   • Dashboard ✅
   • Analysis ❓
   • Settings ❓
```

### 3. فحص Console
```
1. اضغط F12 لفتح Developer Tools
2. اذهب إلى Console
3. ابحث عن هذه الرسائل:
   🚨 Emergency tab fix activated
   ✅ Emergency tab fix completed
```

## 🔍 ما يجب أن تراه

### Dashboard Tab:
- SEO Score: --
- Quick Actions (4 أزرار)
- Recent Recommendations

### Analysis Tab:
- 📝 Meta Tags
- 📋 Headings  
- 🎯 Keywords
- 📄 Content

### Settings Tab:
- 🔑 SEO Configuration
- 🤖 LLM Providers
- 📊 Tools & Export

## 🚨 إذا لم تعمل التبويبات

### الحل السريع في Console:
```javascript
// اكتب هذا في Console:
document.getElementById('analysis').style.display = 'block';
document.getElementById('settings').style.display = 'block';
```

### فحص العناصر:
```javascript
// تحقق من وجود العناصر:
console.log('Dashboard:', !!document.getElementById('dashboard'));
console.log('Analysis:', !!document.getElementById('analysis'));
console.log('Settings:', !!document.getElementById('settings'));
```

### إصلاح يدوي:
```javascript
// إصلاح شامل:
['dashboard', 'analysis', 'settings'].forEach(tab => {
    const btn = document.querySelector(`[data-tab="${tab}"]`);
    const content = document.getElementById(tab);
    
    if (btn && content) {
        btn.onclick = () => {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(t => {
                t.style.display = 'none';
                t.classList.remove('active');
            });
            
            // إظهار التبويب المطلوب
            content.style.display = 'block';
            content.classList.add('active');
            
            // تحديث الأزرار
            document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
        };
    }
});
```

## ✅ علامات النجاح

- ✅ جميع التبويبات تظهر محتواها
- ✅ التبديل يعمل بسلاسة
- ✅ لا توجد أخطاء في Console
- ✅ رسائل "Emergency tab fix" تظهر

## ❌ علامات الفشل

- ❌ تبويب Analysis أو Settings فارغ
- ❌ أخطاء في Console
- ❌ التبديل لا يعمل
- ❌ لا تظهر رسائل "Emergency fix"

## 🔧 حلول إضافية

### إذا استمرت المشكلة:

1. **أعد تشغيل Chrome** كاملاً
2. **احذف الإضافة وأعد تحميلها**
3. **تحقق من ملف popup.html** - يجب أن يحتوي على:
   ```html
   <div id="analysis" class="tab-content">
   <div id="settings" class="tab-content">
   ```

4. **تحقق من ملف popup-modern.css** - يجب أن يحتوي على:
   ```css
   .tab-content { display: none !important; }
   .tab-content.active { display: block !important; }
   ```

## 📞 إذا احتجت مساعدة

1. افتح `test-tabs.html` للتعليمات المفصلة
2. راجع `FIX_TABS_ISSUE.md` للحلول الشاملة
3. تحقق من Console للأخطاء
4. جرب الحلول اليدوية أعلاه

---

**🎯 الهدف: جعل جميع التبويبات تعمل بشكل صحيح!**
