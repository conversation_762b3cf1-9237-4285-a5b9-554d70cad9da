# AI SEO Optimizer Chrome Extension

🚀 إضافة Chrome قوية لتحليل وتحسين SEO باستخدام تقنية الذكاء الاصطناعي

## ✨ الميزات الرئيسية

- **تحليل SEO فوري**: تحليل أي صفحة ويب لأداء SEO
- **بحث الكلمات المفتاحية**: اقتراحات وتحليل الكلمات المفتاحية بالذكاء الاصطناعي
- **تحسين المحتوى**: احصل على توصيات لمحتوى أفضل
- **مراجعة SEO التقنية**: فحص meta tags والعناوين والصور وأكثر
- **مراقبة الأداء**: تحليل سرعة الصفحة و Core Web Vitals
- **دعم متعدد اللغات**: يعمل مع المحتوى بلغات متعددة
- **تكامل LLM**: دعم لمقدمي AI متعددين (OpenAI, Anthropic, إلخ)
- **تصدير التقارير**: إنشاء تقارير SEO مفصلة بصيغة JSON/PDF

## 📥 التثبيت السريع

### للمطورين والاختبار:
```bash
1. حمّل المجلد "Ai SEO Optimizer"
2. افتح Chrome → chrome://extensions/
3. فعّل "Developer mode"
4. انقر "Load unpacked"
5. اختر مجلد الإضافة
6. ✅ جاهز للاستخدام!
```

## 🎯 كيفية الاستخدام

### الخطوة 1: تحليل الصفحة
- انقر على أيقونة الإضافة 🔍
- اضغط "Analyze Page"
- انتظر انتهاء التحليل

### الخطوة 2: عرض النتائج
- **Dashboard**: درجات SEO والتوصيات
- **Analysis**: تحليل مفصل للعناصر
- **Settings**: إعدادات وتخصيص

### الخطوة 3: الميزات المتقدمة
- **Keywords**: توليد كلمات مفتاحية ذكية
- **Speed Test**: اختبار سرعة الصفحة
- **Export**: تصدير التقارير

## ⚙️ الإعداد والتخصيص

### إضافة مقدمي AI:
1. اذهب إلى Settings
2. انقر "Add Provider"
3. اختر المقدم:
   - 🤖 OpenAI (GPT-4, GPT-3.5)
   - 🧠 Anthropic (Claude)
   - 💎 Google Gemini
   - 🌐 OpenRouter
   - 🏠 Ollama (محلي)
   - ⚡ Groq

### تخصيص التحليل:
- **Target Keywords**: كلمات مفتاحية مستهدفة
- **Keyword Density**: كثافة الكلمات المفتاحية المطلوبة
- **Analysis Language**: لغة التحليل

## 🧪 الاختبار

### اختبار سريع:
1. افتح `test-extension.html`
2. انقر على أيقونة الإضافة
3. جرب "Analyze Page"
4. تحقق من النتائج

### اختبار شامل:
- راجع `TESTING_GUIDE.md` للتعليمات المفصلة
- استخدم `LOAD_EXTENSION.md` لحل المشاكل

## 🔧 التفاصيل التقنية

### المعمارية:
- **Manifest V3**: أحدث معايير Chrome
- **Service Worker**: معالجة الخلفية
- **Content Scripts**: استخراج البيانات
- **Popup UI**: واجهة المستخدم

### الملفات الرئيسية:
```
├── manifest.json              # إعدادات الإضافة
├── popup/
│   ├── popup.html            # واجهة المستخدم
│   ├── popup-simple.js       # منطق الواجهة
│   └── popup-modern.css      # التصميم
├── background/
│   └── background-minimal.js # خدمة الخلفية
├── content/
│   └── content-simple.js     # استخراج البيانات
└── assets/icons/             # الأيقونات
```

## 🚀 الميزات المتقدمة

### تحليل ذكي:
- تحليل meta tags
- فحص هيكل العناوين
- تحليل الصور وال alt text
- فحص الروابط الداخلية والخارجية

### تقارير مفصلة:
- درجات SEO مفصلة
- توصيات قابلة للتنفيذ
- تصدير JSON/PDF
- تتبع التقدم

### دعم متعدد المنصات:
- WordPress
- Shopify
- مواقع مخصصة
- جميع أنواع المواقع

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة:
- **لا تفتح النافذة**: تحقق من Developer Tools
- **لا يعمل التحليل**: تأكد من صلاحيات الصفحة
- **نتائج غير دقيقة**: جرب إعادة تحميل الصفحة

### الحصول على المساعدة:
1. افتح Developer Tools (F12)
2. تحقق من Console للأخطاء
3. راجع `TESTING_GUIDE.md`
4. استخدم `test-extension.html` للاختبار

## 📊 مؤشرات الأداء

### ما يتم تحليله:
- **Title Tags**: الطول والتحسين
- **Meta Descriptions**: الوجود والجودة
- **Headings**: الهيكل والتنظيم
- **Content**: الطول والجودة
- **Images**: Alt text والتحسين
- **Links**: الروابط الداخلية والخارجية

### درجات SEO:
- **90-100**: ممتاز 🟢
- **70-89**: جيد 🟡
- **50-69**: يحتاج تحسين 🟠
- **أقل من 50**: ضعيف 🔴

## 🔐 الأمان والخصوصية

- **البيانات محلية**: لا ترسل لخوادم خارجية
- **API Keys آمنة**: محفوظة محلياً فقط
- **لا تتبع**: لا نجمع بيانات شخصية
- **مفتوح المصدر**: كود قابل للمراجعة

## 🚀 التطوير المستقبلي

### قريباً:
- تكامل مع Google Search Console
- تحليل المنافسين
- تتبع الكلمات المفتاحية
- تقارير أكثر تفصيلاً

### مساهمات:
- نرحب بالمساهمات
- راجع issues للمهام المطلوبة
- اتبع معايير الكود

---

**🎉 جاهز للبدء؟ حمّل الإضافة وابدأ تحسين SEO موقعك اليوم!**
