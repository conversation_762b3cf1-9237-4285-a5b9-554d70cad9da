# دليل اختبار إضافة AI SEO Optimizer

## الخطوات الأساسية للاختبار:

### 1. تحميل الإضافة
```
1. افتح Chrome
2. اذهب إلى chrome://extensions/
3. فعّل "Developer mode"
4. انق<PERSON> "Load unpacked"
5. اخت<PERSON> مجلد "Ai SEO Optimizer"
```

### 2. التحقق من التحميل الصحيح
- ✅ يجب أن تظهر الإضافة في قائمة Extensions
- ✅ يجب أن تكون مفعلة (enabled)
- ✅ يجب أن تظهر الأيقونة في شريط الأدوات

### 3. اختبار النافذة المنبثقة
```
1. انقر على أيقونة الإضافة
2. يجب أن تفتح نافذة منبثقة
3. تحقق من ظهور التبويبات الثلاثة:
   - Dashboard
   - Analysis  
   - Settings
```

### 4. اختبار التبديل بين التبويبات
- انقر على كل تبويب
- تأكد من تغيير المحتوى
- تأكد من تمييز التبويب النشط

### 5. اختبار ميزة تحليل الصفحة
```
1. افتح test-extension.html في Chrome
2. انقر على أيقونة الإضافة
3. انقر "Analyze Page"
4. انتظر انتهاء التحليل
5. تحقق من ظهور النتائج في Dashboard
```

### 6. اختبار تبويب Analysis
```
1. بعد تحليل الصفحة
2. انقر على تبويب "Analysis"
3. تحقق من ظهور:
   - Meta Tags Analysis
   - Headings Analysis
   - Keywords Analysis
   - Content Analysis
```

### 7. اختبار توليد الكلمات المفتاحية
```
1. في تبويب Dashboard
2. انقر "Keywords"
3. انتظر انتهاء العملية
4. تحقق من ظهور الكلمات في Settings
```

### 8. اختبار إعدادات LLM Providers
```
1. اذهب إلى تبويب Settings
2. انقر "Add Provider"
3. جرب إضافة provider جديد
4. تحقق من ظهوره في القائمة
```

### 9. اختبار Speed Test
```
1. في تبويب Settings
2. انقر "Speed Test"
3. انتظر النتائج
4. تحقق من ظهور النتائج
```

### 10. اختبار Export
```
1. بعد تحليل صفحة
2. انقر "Export JSON"
3. تحقق من تحميل الملف
```

## الأخطاء الشائعة وحلولها:

### إذا لم تفتح النافذة المنبثقة:
- تحقق من وجود ملف popup.html
- تحقق من مسار الملفات في manifest.json
- افتح Developer Tools وتحقق من الأخطاء

### إذا لم تعمل ميزة التحليل:
- تحقق من Console في Developer Tools
- تأكد من أن background script يعمل
- جرب إعادة تحميل الإضافة

### إذا لم تظهر النتائج:
- تحقق من أن الصفحة قابلة للوصول
- تأكد من أن content script محمل
- جرب صفحة أخرى

## نصائح للاختبار:

### استخدم Developer Tools:
```
1. اضغط F12
2. اذهب إلى Console
3. راقب الرسائل والأخطاء
4. ابحث عن رسائل الإضافة (تبدأ بـ emoji)
```

### اختبر على صفحات مختلفة:
- صفحات عادية (مثل test-extension.html)
- مواقع حقيقية
- صفحات بمحتوى مختلف

### تحقق من الأداء:
- سرعة التحليل
- استجابة الواجهة
- دقة النتائج

## علامات النجاح:

### ✅ الإضافة تعمل بنجاح إذا:
- تفتح النافذة المنبثقة بدون أخطاء
- تعمل جميع التبويبات
- يعمل تحليل الصفحة ويظهر نتائج
- تعمل ميزة توليد الكلمات المفتاحية
- تعمل إعدادات LLM Providers
- يعمل Speed Test
- يعمل Export

### ❌ مؤشرات وجود مشاكل:
- أخطاء في Console
- عدم فتح النافذة المنبثقة
- عدم عمل الأزرار
- عدم ظهور النتائج
- رسائل خطأ

## اختبار متقدم:

### اختبار على مواقع مختلفة:
- مواقع WordPress
- مواقع التجارة الإلكترونية
- مدونات
- مواقع الأخبار

### اختبار الأداء:
- صفحات كبيرة
- صفحات بصور كثيرة
- صفحات بمحتوى قليل

### اختبار الحالات الاستثنائية:
- صفحات بدون title
- صفحات بدون meta description
- صفحات بدون محتوى
