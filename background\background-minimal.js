// AI SEO Optimizer - Minimal Background Service Worker

console.log('AI SEO Optimizer background script starting...');

// Basic extension setup
chrome.runtime.onInstalled.addListener((details) => {
    console.log('AI SEO Optimizer installed:', details.reason);
    
    // Set default settings
    chrome.storage.sync.set({
        keywordDensity: 2,
        autoAnalysis: false,
        targetKeywords: '',
        llmProviders: [
            {
                id: 'openai-default',
                name: 'OpenAI',
                type: 'openai',
                apiKey: '',
                baseUrl: 'https://api.openai.com/v1',
                model: 'gpt-3.5-turbo',
                maxTokens: 4000,
                enabled: true,
                isDefault: true
            }
        ],
        defaultProvider: 'openai-default'
    }).then(() => {
        console.log('Default settings saved');
    }).catch(error => {
        console.error('Error saving default settings:', error);
    });
});

// Handle messages from popup and content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('📨 Received message:', request.action, 'from:', sender.tab?.url || 'popup');

    try {
        switch (request.action) {
            case 'analyzePage':
                handleAnalyzePage(request.data, sendResponse);
                break;

            case 'generateKeywords':
                handleGenerateKeywords(request.data, sendResponse);
                break;

            case 'getPageData':
                handleGetPageData(sender.tab?.id, sendResponse);
                break;

            case 'runSpeedTest':
                handleSpeedTest(request.data, sendResponse);
                break;

            default:
                console.warn('❌ Unknown action:', request.action);
                sendResponse({ success: false, error: 'Unknown action: ' + request.action });
        }
    } catch (error) {
        console.error('❌ Error handling message:', error);
        sendResponse({ success: false, error: 'Internal error: ' + error.message });
    }

    return true; // Keep message channel open for async responses
});

// Simple page analysis
async function handleAnalyzePage(data, sendResponse) {
    try {
        console.log('🔍 Starting page analysis...');

        if (!data || !data.pageData) {
            throw new Error('No page data provided');
        }

        const pageData = data.pageData;
        const targetKeywords = data.targetKeywords || [];

        console.log('📊 Analyzing page:', pageData.url || 'Unknown URL');
        console.log('🎯 Target keywords:', targetKeywords);

        // Validate page data
        if (!pageData.title && !pageData.content) {
            console.warn('⚠️ Page data seems incomplete, proceeding with available data');
        }

        // Basic SEO analysis
        const scoreResult = calculateBasicScore(pageData);
        const analysis = {
            seoScore: scoreResult.seoScore || 0,
            keywordScore: analyzeKeywords(pageData, targetKeywords),
            contentScore: analyzeContent(pageData),
            technicalScore: analyzeTechnical(pageData),
            recommendations: scoreResult.recommendations || [],
            pageData: pageData,
            breakdown: scoreResult.breakdown || {},
            timestamp: new Date().toISOString()
        };

        console.log('✅ Analysis completed successfully:', {
            seoScore: analysis.seoScore,
            keywordScore: analysis.keywordScore,
            contentScore: analysis.contentScore,
            technicalScore: analysis.technicalScore,
            recommendationsCount: analysis.recommendations.length
        });

        sendResponse({ success: true, data: analysis });

    } catch (error) {
        console.error('❌ Error analyzing page:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// Simple keyword generation
async function handleGenerateKeywords(data, sendResponse) {
    try {
        console.log('🎯 Generating keywords with data:', data);

        const pageData = data?.pageData;
        if (!pageData) {
            throw new Error('No page data provided');
        }

        console.log('Generating keywords for:', pageData.url);

        const content = pageData.content || pageData.title || '';
        if (!content.trim()) {
            // Fallback keywords if no content
            const fallbackKeywords = {
                primaryKeywords: ['seo', 'optimization', 'content', 'marketing', 'website'],
                longTailKeywords: ['seo optimization', 'content marketing', 'website analysis'],
                semanticKeywords: ['search engine', 'digital marketing', 'web analytics']
            };

            console.log('Using fallback keywords (no content found)');
            sendResponse({ success: true, data: fallbackKeywords });
            return;
        }

        const words = content.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 3 && word.length < 15)
            .filter(word => !['this', 'that', 'with', 'have', 'will', 'from', 'they', 'been', 'said', 'each', 'which', 'their', 'time', 'would', 'there', 'could', 'other'].includes(word));

        // Count word frequency
        const wordCount = {};
        words.forEach(word => {
            wordCount[word] = (wordCount[word] || 0) + 1;
        });

        // Get top keywords (minimum frequency of 2)
        const topWords = Object.entries(wordCount)
            .filter(([word, count]) => count >= 2)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 15)
            .map(([word]) => word);

        // Generate long-tail keywords from title and headings
        const longTailSources = [
            pageData.title,
            ...(pageData.headings?.h1 || []).map(h => h.text || h),
            ...(pageData.headings?.h2 || []).map(h => h.text || h)
        ].filter(Boolean);

        const longTailKeywords = longTailSources
            .map(text => text.toLowerCase().trim())
            .filter(text => text.length > 10 && text.length < 60)
            .slice(0, 3);

        const keywords = {
            primaryKeywords: topWords.slice(0, 5),
            longTailKeywords: longTailKeywords,
            semanticKeywords: topWords.slice(5, 10)
        };

        console.log('✅ Keywords generated successfully:', keywords);
        sendResponse({ success: true, data: keywords });

    } catch (error) {
        console.error('❌ Error generating keywords:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// Get page data using content script
async function handleGetPageData(tabId, sendResponse) {
    try {
        console.log('📄 Getting page data for tab:', tabId);

        if (!tabId) {
            // Fallback: get active tab
            const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            tabId = activeTab?.id;
            console.log('📄 Using active tab:', tabId);
        }

        if (!tabId) {
            throw new Error('No active tab found');
        }

        // Check if tab is accessible
        const tab = await chrome.tabs.get(tabId);
        if (!tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
            throw new Error('Cannot access this type of page');
        }

        const results = await chrome.scripting.executeScript({
            target: { tabId: tabId },
            function: extractPageData
        });

        if (!results || !results[0] || !results[0].result) {
            throw new Error('Failed to extract page data');
        }

        const pageData = results[0].result;
        console.log('✅ Page data extracted successfully:', pageData.url);
        sendResponse({ success: true, data: pageData });

    } catch (error) {
        console.error('❌ Error getting page data:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// Handle speed test
async function handleSpeedTest(data, sendResponse) {
    try {
        console.log('🚀 Running speed test for:', data.url);

        // For now, return mock data
        // In a real implementation, you would call PageSpeed Insights API
        const mockResults = {
            performance: Math.floor(Math.random() * 30) + 70, // 70-100
            fcp: (Math.random() * 2 + 0.8).toFixed(1) + 's', // 0.8-2.8s
            lcp: (Math.random() * 3 + 1.5).toFixed(1) + 's', // 1.5-4.5s
            cls: (Math.random() * 0.1).toFixed(3), // 0-0.1
            tti: (Math.random() * 4 + 2).toFixed(1) + 's', // 2-6s
            url: data.url,
            timestamp: new Date().toISOString()
        };

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 2000));

        console.log('✅ Speed test completed:', mockResults);
        sendResponse({ success: true, data: mockResults });

    } catch (error) {
        console.error('❌ Speed test error:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// Function to inject into page for data extraction
function extractPageData() {
    console.log('🔍 Extracting page data from:', window.location.href);

    try {
        const data = {
            url: window.location.href,
            title: document.title || 'No title',
            metaDescription: document.querySelector('meta[name="description"]')?.content || '',
            metaKeywords: document.querySelector('meta[name="keywords"]')?.content || '',
            headings: {
                h1: Array.from(document.querySelectorAll('h1')).map(h => ({ text: h.textContent.trim() })),
                h2: Array.from(document.querySelectorAll('h2')).map(h => ({ text: h.textContent.trim() })),
                h3: Array.from(document.querySelectorAll('h3')).map(h => ({ text: h.textContent.trim() }))
            },
            content: document.body.innerText || document.body.textContent || '',
            wordCount: (document.body.innerText || '').split(/\s+/).filter(w => w.length > 0).length,
            images: Array.from(document.querySelectorAll('img')).map(img => ({
                src: img.src || '',
                alt: img.alt || '',
                title: img.title || '',
                hasAlt: !!img.alt
            })),
            links: Array.from(document.querySelectorAll('a')).map(link => ({
                href: link.href || '',
                text: link.textContent.trim(),
                internal: link.hostname === window.location.hostname
            })),
            timestamp: new Date().toISOString()
        };

        console.log('✅ Page data extracted:', {
            title: data.title,
            wordCount: data.wordCount,
            h1Count: data.headings.h1.length,
            imageCount: data.images.length
        });

        return data;
    } catch (error) {
        console.error('❌ Error extracting page data:', error);
        return {
            url: window.location.href,
            title: document.title || 'Error',
            error: error.message,
            timestamp: new Date().toISOString()
        };
    }
}

// Enhanced scoring functions
function calculateBasicScore(pageData) {
    console.log('📊 Calculating SEO score for:', pageData.title);

    let scores = {
        title: 0,
        metaDescription: 0,
        content: 0,
        headings: 0,
        images: 0,
        overall: 0
    };

    // Title analysis (25 points)
    if (pageData.title) {
        const titleLength = pageData.title.length;
        if (titleLength >= 30 && titleLength <= 60) {
            scores.title = 25;
        } else if (titleLength >= 20 && titleLength <= 70) {
            scores.title = 20;
        } else if (titleLength > 0) {
            scores.title = 10;
        }
    }

    // Meta description (25 points)
    if (pageData.metaDescription) {
        const descLength = pageData.metaDescription.length;
        if (descLength >= 120 && descLength <= 160) {
            scores.metaDescription = 25;
        } else if (descLength >= 100 && descLength <= 180) {
            scores.metaDescription = 20;
        } else if (descLength > 0) {
            scores.metaDescription = 10;
        }
    }

    // Content analysis (25 points)
    if (pageData.wordCount > 500) {
        scores.content = 25;
    } else if (pageData.wordCount > 300) {
        scores.content = 20;
    } else if (pageData.wordCount > 150) {
        scores.content = 15;
    } else if (pageData.wordCount > 50) {
        scores.content = 10;
    }

    // Headings structure (15 points)
    if (pageData.headings) {
        const h1Count = pageData.headings.h1?.length || 0;
        const h2Count = pageData.headings.h2?.length || 0;

        if (h1Count === 1 && h2Count > 0) {
            scores.headings = 15;
        } else if (h1Count === 1) {
            scores.headings = 10;
        } else if (h1Count > 0) {
            scores.headings = 5;
        }
    }

    // Images with alt text (10 points)
    if (pageData.images && pageData.images.length > 0) {
        const imagesWithAlt = pageData.images.filter(img => img.hasAlt).length;
        const altRatio = imagesWithAlt / pageData.images.length;
        scores.images = Math.round(10 * altRatio);
    }

    // Calculate overall score
    scores.overall = scores.title + scores.metaDescription + scores.content + scores.headings + scores.images;

    console.log('📈 SEO Scores calculated:', scores);

    return {
        seoScore: Math.min(scores.overall, 100),
        keywordScore: Math.min(scores.title + scores.content, 100),
        contentScore: Math.min(scores.content + scores.headings, 100),
        technicalScore: Math.min(scores.metaDescription + scores.images, 100),
        breakdown: scores,
        recommendations: generateRecommendations(pageData, scores)
    };
}

function generateRecommendations(pageData, scores) {
    const recommendations = [];

    // Title recommendations
    if (scores.title < 20) {
        recommendations.push({
            type: 'title',
            title: 'Optimize Title Tag',
            description: pageData.title ?
                `Title length is ${pageData.title.length} chars. Aim for 30-60 characters.` :
                'Add a descriptive title tag to your page.',
            priority: 'high'
        });
    }

    // Meta description recommendations
    if (scores.metaDescription < 20) {
        recommendations.push({
            type: 'meta',
            title: 'Improve Meta Description',
            description: pageData.metaDescription ?
                `Meta description is ${pageData.metaDescription.length} chars. Aim for 120-160 characters.` :
                'Add a compelling meta description to improve click-through rates.',
            priority: 'high'
        });
    }

    // Content recommendations
    if (scores.content < 20) {
        recommendations.push({
            type: 'content',
            title: 'Increase Content Length',
            description: `Page has ${pageData.wordCount} words. Consider adding more valuable content (aim for 500+ words).`,
            priority: 'medium'
        });
    }

    // Headings recommendations
    if (scores.headings < 10) {
        const h1Count = pageData.headings?.h1?.length || 0;
        if (h1Count === 0) {
            recommendations.push({
                type: 'headings',
                title: 'Add H1 Tag',
                description: 'Every page should have exactly one H1 tag for better SEO structure.',
                priority: 'high'
            });
        } else if (h1Count > 1) {
            recommendations.push({
                type: 'headings',
                title: 'Fix H1 Structure',
                description: `Found ${h1Count} H1 tags. Use only one H1 per page.`,
                priority: 'medium'
            });
        }
    }

    // Images recommendations
    if (scores.images < 8 && pageData.images?.length > 0) {
        const missingAlt = pageData.images.filter(img => !img.hasAlt).length;
        recommendations.push({
            type: 'images',
            title: 'Add Alt Text to Images',
            description: `${missingAlt} image(s) missing alt text. Add descriptive alt text for better accessibility and SEO.`,
            priority: 'medium'
        });
    }

    return recommendations;
}

function analyzeKeywords(pageData, targetKeywords) {
    if (!targetKeywords || targetKeywords.length === 0) {
        return 50; // Neutral score if no target keywords
    }

    let score = 0;
    const content = (pageData.content || '').toLowerCase();
    const title = (pageData.title || '').toLowerCase();

    targetKeywords.forEach(keyword => {
        const keywordLower = keyword.toLowerCase();
        
        // Check if keyword is in title (higher weight)
        if (title.includes(keywordLower)) {
            score += 40;
        }
        
        // Check if keyword is in content
        if (content.includes(keywordLower)) {
            score += 20;
        }
    });

    return Math.min(100, score / targetKeywords.length);
}

function analyzeContent(pageData) {
    let score = 0;
    
    // Word count scoring (40 points)
    if (pageData.wordCount >= 500) {
        score += 40;
    } else if (pageData.wordCount >= 300) {
        score += 30;
    } else if (pageData.wordCount >= 150) {
        score += 20;
    } else if (pageData.wordCount >= 50) {
        score += 10;
    }

    // Heading structure (60 points)
    if (pageData.headings) {
        // H1 analysis (30 points)
        if (pageData.headings.h1 && pageData.headings.h1.length === 1) {
            score += 30;
        } else if (pageData.headings.h1 && pageData.headings.h1.length > 1) {
            score += 10; // Multiple H1s is not ideal
        }
        
        // H2 analysis (30 points)
        if (pageData.headings.h2 && pageData.headings.h2.length > 0) {
            score += 30;
        }
    }

    return Math.min(100, score);
}

function analyzeTechnical(pageData) {
    let score = 0;

    // HTTPS check (30 points)
    if (pageData.url && pageData.url.startsWith('https://')) {
        score += 30;
    }

    // Meta description exists (35 points)
    if (pageData.metaDescription && pageData.metaDescription.length > 0) {
        score += 35;
    }

    // Title exists (35 points)
    if (pageData.title && pageData.title.length > 0) {
        score += 35;
    }

    return Math.min(100, score);
}

function generateRecommendations(pageData, targetKeywords) {
    const recommendations = [];

    // Title recommendations
    if (!pageData.title) {
        recommendations.push({
            type: 'title',
            title: 'Add page title',
            description: 'Page is missing a title tag',
            priority: 'high'
        });
    } else if (pageData.title.length < 30) {
        recommendations.push({
            type: 'title',
            title: 'Expand page title',
            description: 'Page title should be 30-60 characters long',
            priority: 'high'
        });
    } else if (pageData.title.length > 60) {
        recommendations.push({
            type: 'title',
            title: 'Shorten page title',
            description: 'Page title should be under 60 characters',
            priority: 'medium'
        });
    }

    // Meta description recommendations
    if (!pageData.metaDescription) {
        recommendations.push({
            type: 'meta',
            title: 'Add meta description',
            description: 'Page is missing a meta description',
            priority: 'high'
        });
    } else if (pageData.metaDescription.length < 120) {
        recommendations.push({
            type: 'meta',
            title: 'Expand meta description',
            description: 'Meta description should be 120-160 characters',
            priority: 'medium'
        });
    } else if (pageData.metaDescription.length > 160) {
        recommendations.push({
            type: 'meta',
            title: 'Shorten meta description',
            description: 'Meta description should be under 160 characters',
            priority: 'medium'
        });
    }

    // Content recommendations
    if (pageData.wordCount < 300) {
        recommendations.push({
            type: 'content',
            title: 'Increase content length',
            description: `Content has ${pageData.wordCount} words. Aim for at least 300 words.`,
            priority: 'medium'
        });
    }

    // Heading recommendations
    if (pageData.headings) {
        if (!pageData.headings.h1 || pageData.headings.h1.length === 0) {
            recommendations.push({
                type: 'heading',
                title: 'Add H1 heading',
                description: 'Page should have exactly one H1 heading',
                priority: 'high'
            });
        } else if (pageData.headings.h1.length > 1) {
            recommendations.push({
                type: 'heading',
                title: 'Use only one H1',
                description: 'Page should have only one H1 heading',
                priority: 'medium'
            });
        }

        if (!pageData.headings.h2 || pageData.headings.h2.length === 0) {
            recommendations.push({
                type: 'heading',
                title: 'Add H2 subheadings',
                description: 'Use H2 tags to structure your content',
                priority: 'low'
            });
        }
    }

    // Image recommendations
    if (pageData.images && pageData.images.length > 0) {
        const imagesWithoutAlt = pageData.images.filter(img => !img.alt || img.alt.trim().length === 0).length;
        if (imagesWithoutAlt > 0) {
            recommendations.push({
                type: 'image',
                title: 'Add alt text to images',
                description: `${imagesWithoutAlt} of ${pageData.images.length} images are missing alt text`,
                priority: 'medium'
            });
        }
    }

    return recommendations;
}

console.log('AI SEO Optimizer background script loaded successfully');
