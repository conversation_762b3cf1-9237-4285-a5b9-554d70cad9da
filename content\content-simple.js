// AI SEO Optimizer - Simple Content Script

console.log('AI SEO Optimizer content script loaded on:', window.location.href);

// Simple message listener
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('Content script received message:', request.action);
    
    try {
        switch (request.action) {
            case 'extractPageData':
                const pageData = extractPageData();
                sendResponse({ success: true, data: pageData });
                break;
                
            case 'highlightSEOIssues':
                // Simple highlighting - just log for now
                console.log('Would highlight SEO issues:', request.data);
                sendResponse({ success: true });
                break;
                
            default:
                sendResponse({ success: false, error: 'Unknown action' });
        }
    } catch (error) {
        console.error('Content script error:', error);
        sendResponse({ success: false, error: error.message });
    }
    
    return true;
});

// Extract page data function
function extractPageData() {
    try {
        console.log('🔍 Extracting page data from:', window.location.href);

        const data = {
            url: window.location.href,
            title: document.title || '',
            metaDescription: getMetaContent('description'),
            metaKeywords: getMetaContent('keywords'),
            headings: extractHeadings(),
            content: document.body ? document.body.innerText : '',
            wordCount: document.body ? document.body.innerText.split(/\s+/).filter(w => w.length > 0).length : 0,
            images: extractImages(),
            links: extractLinks(),
            timestamp: new Date().toISOString()
        };

        console.log('✅ Page data extracted successfully:', {
            url: data.url,
            title: data.title,
            wordCount: data.wordCount,
            h1Count: data.headings.h1.length,
            imageCount: data.images.length
        });

        return data;

    } catch (error) {
        console.error('❌ Error extracting page data:', error);
        return {
            url: window.location.href,
            title: document.title || '',
            metaDescription: '',
            metaKeywords: '',
            headings: { h1: [], h2: [], h3: [] },
            content: '',
            wordCount: 0,
            images: [],
            links: [],
            error: error.message,
            timestamp: new Date().toISOString()
        };
    }
}

function getMetaContent(name) {
    try {
        const meta = document.querySelector(`meta[name="${name}"], meta[property="${name}"]`);
        return meta ? meta.content : '';
    } catch (error) {
        return '';
    }
}

function extractHeadings() {
    try {
        return {
            h1: Array.from(document.querySelectorAll('h1')).map(h => ({ text: h.textContent.trim() })),
            h2: Array.from(document.querySelectorAll('h2')).map(h => ({ text: h.textContent.trim() })),
            h3: Array.from(document.querySelectorAll('h3')).map(h => ({ text: h.textContent.trim() }))
        };
    } catch (error) {
        return { h1: [], h2: [], h3: [] };
    }
}

function extractImages() {
    try {
        return Array.from(document.querySelectorAll('img')).map(img => ({
            src: img.src || '',
            alt: img.alt || '',
            title: img.title || '',
            hasAlt: !!(img.alt && img.alt.trim())
        }));
    } catch (error) {
        console.error('Error extracting images:', error);
        return [];
    }
}

function extractLinks() {
    try {
        return Array.from(document.querySelectorAll('a[href]')).map(link => ({
            href: link.href || '',
            text: link.textContent.trim(),
            internal: link.hostname === window.location.hostname
        }));
    } catch (error) {
        return [];
    }
}

console.log('AI SEO Optimizer content script ready');
