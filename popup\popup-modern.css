/* AI SEO Optimizer - Modern Clean Design */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 380px;
    height: 600px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f8fafc;
    color: #1e293b;
    overflow: hidden;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: white;
}

/* Header */
.header {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    padding: 20px;
    text-align: center;
    position: relative;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
}

.logo-icon {
    width: 28px;
    height: 28px;
}

.logo h1 {
    font-size: 20px;
    font-weight: 700;
}

.nav-tabs {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.tab-btn {
    padding: 8px 16px;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 20px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 80px;
}

.tab-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.tab-btn.active {
    background: white;
    color: #3b82f6;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8fafc;
}

.tab-content {
    display: none !important;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tab-content.active {
    display: block !important;
    opacity: 1;
}

/* Dashboard */
.dashboard-grid {
    display: grid;
    gap: 20px;
}

.seo-score-section {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.score-header {
    text-align: center;
    margin-bottom: 20px;
}

.main-score {
    font-size: 48px;
    font-weight: 800;
    color: #3b82f6;
    margin-bottom: 5px;
}

.score-label {
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
}

.score-breakdown {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

.score-item {
    text-align: center;
    padding: 15px;
    background: #f1f5f9;
    border-radius: 12px;
}

.score-item-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.score-item-value.good { color: #10b981; }
.score-item-value.medium { color: #f59e0b; }
.score-item-value.poor { color: #ef4444; }

.score-item-label {
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
}

/* Action Buttons */
.action-section {
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    margin-bottom: 20px;
}

.section-title {
    font-size: 16px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.action-btn {
    padding: 12px 16px;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    transition: all 0.2s ease;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
    background: #f1f5f9;
    color: #475569;
    border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
    background: #e2e8f0;
    color: #334155;
}

/* Recommendations */
.recommendations-section {
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.recommendations-list {
    display: grid;
    gap: 12px;
}

.recommendation-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 12px;
    border-left: 4px solid #3b82f6;
    transition: all 0.2s ease;
}

.recommendation-item:hover {
    background: #f1f5f9;
    transform: translateX(2px);
}

.rec-icon {
    font-size: 18px;
    margin-top: 2px;
    flex-shrink: 0;
}

.rec-content {
    flex: 1;
}

.rec-title {
    font-weight: 600;
    font-size: 14px;
    color: #1e293b;
    margin-bottom: 4px;
}

.rec-description {
    font-size: 12px;
    color: #64748b;
    line-height: 1.4;
}

/* Analysis Tab */
.analysis-container {
    display: grid;
    gap: 20px;
}

.analysis-card {
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.analysis-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.analysis-title {
    font-size: 16px;
    font-weight: 700;
    color: #1e293b;
}

.status-badge {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-good {
    background: #dcfce7;
    color: #166534;
}

.status-medium {
    background: #fef3c7;
    color: #92400e;
}

.status-poor {
    background: #fee2e2;
    color: #991b1b;
}

.analysis-content {
    display: grid;
    gap: 10px;
}

.analysis-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8fafc;
    border-radius: 8px;
    font-size: 12px;
}

.analysis-label {
    color: #64748b;
    font-weight: 500;
}

.analysis-value {
    font-weight: 600;
    color: #1e293b;
}

/* Settings Tab */
.settings-container {
    display: grid;
    gap: 20px;
}

.settings-card {
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.form-group {
    margin-bottom: 16px;
}

.form-label {
    display: block;
    font-size: 13px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 6px;
}

.form-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 13px;
    transition: border-color 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-help {
    font-size: 11px;
    color: #6b7280;
    margin-top: 4px;
}

/* LLM Providers */
.provider-list {
    display: grid;
    gap: 12px;
}

.provider-item {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 16px;
    transition: all 0.2s ease;
}

.provider-item:hover {
    border-color: #3b82f6;
    background: #f1f5f9;
}

.provider-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.provider-name {
    font-weight: 600;
    color: #1e293b;
    font-size: 14px;
}

.provider-status {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.provider-status.connected {
    background: #dcfce7;
    color: #166534;
}

.provider-status.disconnected {
    background: #fee2e2;
    color: #991b1b;
}

.provider-actions {
    display: flex;
    gap: 6px;
    margin-top: 8px;
}

.btn-icon {
    padding: 6px 8px;
    border: 1px solid #d1d5db;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    color: #6b7280;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    background: #f3f4f6;
    color: #374151;
}

/* Loading States */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #64748b;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e2e8f0;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Scrollbar */
.main-content::-webkit-scrollbar {
    width: 6px;
}

.main-content::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.main-content::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mb-0 {
    margin-bottom: 0 !important;
}
