// AI SEO Optimizer - Simple Popup Script

console.log('AI SEO Optimizer popup loading...');

class SimplePopup {
    constructor() {
        this.currentTab = 'dashboard';
        this.settings = {};
        this.analysisData = null;
        this.llmProviders = [];
        this.selectedProviderTemplate = null;

        this.init();
    }

    async init() {
        try {
            await this.loadSettings();
            await this.loadLLMProviders();
            this.setupEventListeners();
            this.setupTabNavigation();
            this.updateUI();
            this.renderLLMProviders();
            console.log('Popup initialized successfully');
        } catch (error) {
            console.error('Error initializing popup:', error);
        }
    }

    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Dashboard actions
        const analyzeBtn = document.getElementById('analyze-btn');
        if (analyzeBtn) {
            analyzeBtn.addEventListener('click', () => {
                this.analyzePage();
            });
        }

        const generateBtn = document.getElementById('generate-keywords-btn');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => {
                this.generateKeywords();
            });
        }

        const speedBtn = document.getElementById('check-speed-btn');
        if (speedBtn) {
            speedBtn.addEventListener('click', () => {
                this.checkPageSpeed();
            });
        }

        const exportBtn = document.getElementById('export-report-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportReport();
            });
        }

        // Settings tab buttons
        const speedTestBtn = document.getElementById('speed-test-btn');
        if (speedTestBtn) {
            speedTestBtn.addEventListener('click', () => {
                this.runSpeedTest();
            });
        }

        const exportJsonBtn = document.getElementById('export-json-btn');
        if (exportJsonBtn) {
            exportJsonBtn.addEventListener('click', () => {
                this.exportReport();
            });
        }

        const exportPdfBtn = document.getElementById('export-pdf-btn');
        if (exportPdfBtn) {
            exportPdfBtn.addEventListener('click', () => {
                this.exportPDF();
            });
        }

        const clearDataBtn = document.getElementById('clear-data-btn');
        if (clearDataBtn) {
            clearDataBtn.addEventListener('click', () => {
                this.clearData();
            });
        }

        const addProviderBtn = document.getElementById('add-provider-btn');
        if (addProviderBtn) {
            addProviderBtn.addEventListener('click', () => {
                this.showAddProviderModal();
            });
        }

        // Keyword density slider
        const densitySlider = document.getElementById('keyword-density');
        const densityValue = document.getElementById('density-value');
        if (densitySlider && densityValue) {
            densitySlider.addEventListener('input', (e) => {
                densityValue.textContent = e.target.value + '%';
                this.settings.keywordDensity = parseFloat(e.target.value);
                this.saveSettings();
            });
        }

        // Settings
        const saveBtn = document.getElementById('save-settings');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveSettings();
            });
        }

        const resetBtn = document.getElementById('reset-settings');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetSettings();
            });
        }

        // Keyword density slider
        const densitySlider = document.getElementById('keyword-density');
        const densityValue = document.getElementById('density-value');
        if (densitySlider && densityValue) {
            densitySlider.addEventListener('input', (e) => {
                densityValue.textContent = e.target.value + '%';
            });
        }

        // LLM Provider management
        const addProviderBtn = document.getElementById('add-llm-provider');
        if (addProviderBtn) {
            addProviderBtn.addEventListener('click', () => {
                this.showAddProviderModal();
            });
        }

        // Modal events
        const closeModal = document.getElementById('close-modal');
        const cancelBtn = document.getElementById('cancel-add-provider');
        const confirmBtn = document.getElementById('confirm-add-provider');

        if (closeModal) {
            closeModal.addEventListener('click', () => {
                this.hideAddProviderModal();
            });
        }

        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.hideAddProviderModal();
            });
        }

        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => {
                this.addLLMProvider();
            });
        }

        // Provider template selection
        document.querySelectorAll('.provider-template').forEach(template => {
            template.addEventListener('click', () => {
                this.selectProviderTemplate(template.dataset.provider);
            });
        });
    }

    setupTabNavigation() {
        this.switchTab('dashboard');
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        const activeBtn = document.querySelector(`[data-tab="${tabName}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        const activeContent = document.getElementById(tabName);
        if (activeContent) {
            activeContent.classList.add('active');
        }

        this.currentTab = tabName;
    }

    async loadSettings() {
        try {
            // Check if chrome.storage is available (extension context)
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
                const result = await chrome.storage.sync.get([
                    'targetKeywords',
                    'keywordDensity',
                    'defaultProvider'
                ]);

                this.settings = {
                    targetKeywords: result.targetKeywords || '',
                    keywordDensity: result.keywordDensity || 2,
                    defaultProvider: result.defaultProvider || ''
                };
            } else {
                // Fallback for testing outside extension context
                console.warn('Chrome storage not available, using localStorage fallback');
                this.settings = {
                    targetKeywords: localStorage.getItem('targetKeywords') || '',
                    keywordDensity: parseInt(localStorage.getItem('keywordDensity')) || 2,
                    defaultProvider: localStorage.getItem('defaultProvider') || ''
                };
            }

            this.updateSettingsUI();
        } catch (error) {
            console.error('Error loading settings:', error);
            // Fallback to default settings
            this.settings = {
                targetKeywords: '',
                keywordDensity: 2,
                defaultProvider: ''
            };
        }
    }

    async loadLLMProviders() {
        try {
            let providers = [];

            // Check if chrome.storage is available (extension context)
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
                const result = await chrome.storage.sync.get(['llmProviders']);
                providers = result.llmProviders || [];
            } else {
                // Fallback for testing outside extension context
                console.warn('Chrome storage not available, using localStorage fallback');
                const stored = localStorage.getItem('llmProviders');
                providers = stored ? JSON.parse(stored) : [];
            }

            this.llmProviders = providers;

            // Add default providers if none exist
            if (this.llmProviders.length === 0) {
                this.llmProviders = [
                    {
                        id: 'openai-default',
                        name: 'OpenAI',
                        type: 'openai',
                        apiKey: '',
                        baseUrl: 'https://api.openai.com/v1',
                        model: 'gpt-3.5-turbo',
                        maxTokens: 4000,
                        enabled: true,
                        isDefault: false
                    }
                ];
                await this.saveLLMProviders();
            }
        } catch (error) {
            console.error('Error loading LLM providers:', error);
            this.llmProviders = [];
        }
    }

    updateSettingsUI() {
        const elements = {
            'target-keywords': this.settings.targetKeywords,
            'keyword-density': this.settings.keywordDensity,
            'default-provider': this.settings.defaultProvider
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.value = value;
            }
        });

        const densityValue = document.getElementById('density-value');
        if (densityValue) {
            densityValue.textContent = this.settings.keywordDensity + '%';
        }
    }

    async saveSettings() {
        try {
            const settings = {
                targetKeywords: this.getElementValue('target-keywords'),
                keywordDensity: parseFloat(this.getElementValue('keyword-density')),
                defaultProvider: this.getElementValue('default-provider')
            };

            // Check if chrome.storage is available (extension context)
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
                await chrome.storage.sync.set(settings);
            } else {
                // Fallback for testing outside extension context
                console.warn('Chrome storage not available, using localStorage fallback');
                Object.entries(settings).forEach(([key, value]) => {
                    localStorage.setItem(key, value);
                });
            }

            this.settings = settings;
            this.showNotification('Settings saved successfully!', 'success');
        } catch (error) {
            console.error('Error saving settings:', error);
            this.showNotification('Error saving settings', 'error');
        }
    }

    async resetSettings() {
        try {
            // Check if chrome.storage is available (extension context)
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
                await chrome.storage.sync.clear();
            } else {
                // Fallback for testing outside extension context
                console.warn('Chrome storage not available, using localStorage fallback');
                localStorage.clear();
            }

            this.settings = {
                targetKeywords: '',
                keywordDensity: 2,
                defaultProvider: ''
            };

            this.llmProviders = [];
            await this.loadLLMProviders(); // This will recreate default providers

            this.updateSettingsUI();
            this.renderLLMProviders();
            this.showNotification('Settings reset to default', 'success');
        } catch (error) {
            console.error('Error resetting settings:', error);
            this.showNotification('Error resetting settings', 'error');
        }
    }

    async analyzePage() {
        this.showLoading('Analyzing page...');

        try {
            // Check if we're in extension context
            if (typeof chrome !== 'undefined' && chrome.tabs && chrome.runtime) {
                try {
                    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
                    const pageData = await this.getPageData(tab?.id);

                    const response = await chrome.runtime.sendMessage({
                        action: 'analyzePage',
                        data: {
                            pageData: pageData,
                            targetKeywords: this.settings.targetKeywords.split(',').map(k => k.trim()).filter(k => k)
                        }
                    });

                    if (response && response.success) {
                        this.analysisData = response.data;
                        this.updateDashboard(response.data);
                        this.updateAnalysisTab(response.data);
                    } else {
                        throw new Error(response?.error || 'Analysis failed');
                    }
                } catch (chromeError) {
                    console.warn('Chrome API error, using mock analysis:', chromeError);
                    const mockAnalysis = this.getMockAnalysis();
                    this.analysisData = mockAnalysis;
                    this.updateDashboard(mockAnalysis);
                    this.updateAnalysisTab(mockAnalysis);
                }
            } else {
                // Fallback for testing outside extension context
                console.warn('Chrome APIs not available, using mock analysis');
                const mockAnalysis = this.getMockAnalysis();
                this.analysisData = mockAnalysis;
                this.updateDashboard(mockAnalysis);
                this.updateAnalysisTab(mockAnalysis);
            }

        } catch (error) {
            console.error('Analysis error:', error);
            this.showNotification('Error analyzing page: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async generateKeywords() {
        this.showLoading('Generating keywords...');

        try {
            // Check if we're in extension context
            if (typeof chrome !== 'undefined' && chrome.tabs && chrome.runtime) {
                const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

                const pageData = await this.getPageData(tab?.id);

                if (!pageData || pageData.error) {
                    throw new Error('Could not get page data');
                }

                const response = await chrome.runtime.sendMessage({
                    action: 'generateKeywords',
                    data: { pageData: pageData }
                });

                if (response && response.success) {
                    const keywords = [
                        ...(response.data.primaryKeywords || []),
                        ...(response.data.semanticKeywords || [])
                    ];

                    const keywordsElement = document.getElementById('target-keywords');
                    if (keywordsElement) {
                        keywordsElement.value = keywords.join(', ');
                    }

                    this.showNotification('Keywords generated successfully!', 'success');
                } else {
                    throw new Error(response.error || 'Keyword generation failed');
                }
            } else {
                // Fallback for testing outside extension context
                console.warn('Chrome APIs not available, using mock keywords');
                const mockKeywords = ['seo', 'optimization', 'content', 'keywords', 'analysis'];

                const keywordsElement = document.getElementById('target-keywords');
                if (keywordsElement) {
                    keywordsElement.value = mockKeywords.join(', ');
                }

                this.showNotification('Mock keywords generated for testing!', 'success');
            }

        } catch (error) {
            console.error('Keyword generation error:', error);
            this.showNotification('Error generating keywords: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async getPageData(tabId) {
        try {
            console.log('📄 Getting page data for tab:', tabId);

            // Check if we're in extension context
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                const response = await chrome.runtime.sendMessage({
                    action: 'getPageData'
                });

                console.log('📄 Page data response:', response);

                if (response && response.success && response.data) {
                    return response.data;
                } else {
                    console.warn('Invalid page data response, using fallback');
                    return this.getMockPageData();
                }
            } else {
                console.warn('Chrome APIs not available, using mock data');
                return this.getMockPageData();
            }
        } catch (error) {
            console.error('Error getting page data:', error);
            return this.getMockPageData();
        }
    }

    updateDashboard(analysis) {
        console.log('🎯 Updating dashboard with analysis:', analysis);

        // Update main SEO score
        this.setElementText('seo-score', analysis.seoScore || '--');

        // Update individual scores with styling
        const keywordClass = this.getScoreClass(analysis.keywordScore);
        const contentClass = this.getScoreClass(analysis.contentScore);
        const technicalClass = this.getScoreClass(analysis.technicalScore);

        // Update score values and apply classes
        const keywordElement = document.getElementById('keyword-score');
        const contentElement = document.getElementById('content-score');
        const technicalElement = document.getElementById('technical-score');
        const overallElement = document.getElementById('overall-health');

        if (keywordElement) {
            keywordElement.textContent = analysis.keywordScore || '--';
            keywordElement.className = `score-item-value ${keywordClass}`;
        }

        if (contentElement) {
            contentElement.textContent = analysis.contentScore || '--';
            contentElement.className = `score-item-value ${contentClass}`;
        }

        if (technicalElement) {
            technicalElement.textContent = analysis.technicalScore || '--';
            technicalElement.className = `score-item-value ${technicalClass}`;
        }

        if (overallElement) {
            const overallScore = Math.round((analysis.seoScore + analysis.keywordScore + analysis.contentScore + analysis.technicalScore) / 4);
            overallElement.textContent = overallScore;
            overallElement.className = `score-item-value ${this.getScoreClass(overallScore)}`;
        }

        // Update recommendations
        const recommendationsList = document.getElementById('recommendations-list');
        if (recommendationsList && analysis.recommendations) {
            recommendationsList.innerHTML = '';

            analysis.recommendations.forEach(rec => {
                const recElement = document.createElement('div');
                recElement.className = 'recommendation-item';
                recElement.innerHTML = `
                    <div class="rec-icon">${this.getRecommendationIcon(rec.type)}</div>
                    <div class="rec-content">
                        <div class="rec-title">${rec.title}</div>
                        <div class="rec-description">${rec.description}</div>
                    </div>
                `;
                recommendationsList.appendChild(recElement);
            });
        }
    }

    getScoreClass(score) {
        if (score >= 80) return 'good';
        if (score >= 60) return 'medium';
        return 'poor';
    }

    updateAnalysisTab(analysis) {
        console.log('📊 Updating Analysis tab with:', analysis);

        // Update Meta Tags Analysis
        const metaAnalysis = document.getElementById('meta-analysis');
        if (metaAnalysis) {
            metaAnalysis.innerHTML = `
                <div class="analysis-item">
                    <span class="analysis-label">Title Length</span>
                    <span class="analysis-value">${analysis.pageData?.title?.length || 0} chars</span>
                </div>
                <div class="analysis-item">
                    <span class="analysis-label">Description</span>
                    <span class="analysis-value">${analysis.pageData?.metaDescription ? 'Present' : 'Missing'}</span>
                </div>
                <div class="analysis-item">
                    <span class="analysis-label">Score</span>
                    <span class="analysis-value">${analysis.breakdown?.title || analysis.seoScore}/100</span>
                </div>
            `;
        }

        // Update status badge for meta
        const metaStatus = document.getElementById('meta-status');
        if (metaStatus) {
            const score = analysis.breakdown?.title || analysis.seoScore;
            metaStatus.className = `status-badge ${this.getStatusClass(score)}`;
            metaStatus.textContent = this.getStatusText(score);
        }

        // Update Headings Analysis
        const headingsAnalysis = document.getElementById('headings-analysis');
        if (headingsAnalysis) {
            const h1Count = analysis.pageData?.headings?.h1?.length || 0;
            const h2Count = analysis.pageData?.headings?.h2?.length || 0;
            const h3Count = analysis.pageData?.headings?.h3?.length || 0;

            headingsAnalysis.innerHTML = `
                <div class="analysis-item">
                    <span class="analysis-label">H1 Tags</span>
                    <span class="analysis-value">${h1Count} found</span>
                </div>
                <div class="analysis-item">
                    <span class="analysis-label">H2 Tags</span>
                    <span class="analysis-value">${h2Count} found</span>
                </div>
                <div class="analysis-item">
                    <span class="analysis-label">Structure</span>
                    <span class="analysis-value">${h1Count === 1 ? 'Good' : 'Needs work'}</span>
                </div>
            `;
        }

        // Update headings status
        const headingsStatus = document.getElementById('headings-status');
        if (headingsStatus) {
            const score = analysis.breakdown?.headings || analysis.technicalScore;
            headingsStatus.className = `status-badge ${this.getStatusClass(score)}`;
            headingsStatus.textContent = this.getStatusText(score);
        }

        // Update Keywords Analysis
        const keywordAnalysis = document.getElementById('keyword-analysis');
        if (keywordAnalysis) {
            keywordAnalysis.innerHTML = `
                <div class="analysis-item">
                    <span class="analysis-label">Density</span>
                    <span class="analysis-value">2.1%</span>
                </div>
                <div class="analysis-item">
                    <span class="analysis-label">Distribution</span>
                    <span class="analysis-value">Even</span>
                </div>
                <div class="analysis-item">
                    <span class="analysis-label">Score</span>
                    <span class="analysis-value">${analysis.keywordScore || 0}/100</span>
                </div>
            `;
        }

        // Update keywords status
        const keywordsStatus = document.getElementById('keywords-status');
        if (keywordsStatus) {
            const score = analysis.keywordScore || 0;
            keywordsStatus.className = `status-badge ${this.getStatusClass(score)}`;
            keywordsStatus.textContent = this.getStatusText(score);
        }

        // Update Content Analysis
        const contentAnalysis = document.getElementById('content-analysis');
        if (contentAnalysis) {
            const wordCount = analysis.pageData?.wordCount || 0;
            const imageCount = analysis.pageData?.images?.length || 0;
            const imagesWithAlt = analysis.pageData?.images?.filter(img => img.hasAlt)?.length || 0;

            contentAnalysis.innerHTML = `
                <div class="analysis-item">
                    <span class="analysis-label">Word Count</span>
                    <span class="analysis-value">${wordCount} words</span>
                </div>
                <div class="analysis-item">
                    <span class="analysis-label">Images</span>
                    <span class="analysis-value">${imageCount} found</span>
                </div>
                <div class="analysis-item">
                    <span class="analysis-label">Alt Text</span>
                    <span class="analysis-value">${imagesWithAlt}/${imageCount} have alt</span>
                </div>
            `;
        }

        // Update content status
        const contentStatus = document.getElementById('content-status');
        if (contentStatus) {
            const score = analysis.contentScore || 0;
            contentStatus.className = `status-badge ${this.getStatusClass(score)}`;
            contentStatus.textContent = this.getStatusText(score);
        }

        console.log('✅ Analysis tab updated successfully');
    }

    getStatusClass(score) {
        if (score >= 80) return 'status-good';
        if (score >= 60) return 'status-medium';
        return 'status-poor';
    }

    getStatusText(score) {
        if (score >= 80) return 'Good';
        if (score >= 60) return 'Medium';
        return 'Poor';
    }

    getRecommendationIcon(type) {
        const icons = {
            title: '📝',
            meta: '🏷️',
            heading: '📋',
            content: '📄',
            image: '🖼️',
            link: '🔗',
            keyword: '🎯',
            technical: '⚙️'
        };
        return icons[type] || '💡';
    }

    // LLM Provider Management Methods
    async saveLLMProviders() {
        try {
            // Check if chrome.storage is available (extension context)
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
                await chrome.storage.sync.set({ llmProviders: this.llmProviders });
            } else {
                // Fallback for testing outside extension context
                console.warn('Chrome storage not available, using localStorage fallback');
                localStorage.setItem('llmProviders', JSON.stringify(this.llmProviders));
            }
        } catch (error) {
            console.error('Error saving LLM providers:', error);
        }
    }

    renderLLMProviders() {
        console.log('🤖 Rendering LLM providers:', this.llmProviders);

        const container = document.getElementById('llm-providers-list');
        if (!container) {
            console.warn('LLM providers container not found');
            return;
        }

        // Clear existing content
        container.innerHTML = '';

        if (this.llmProviders.length === 0) {
            container.innerHTML = `
                <div class="provider-item">
                    <div class="provider-header">
                        <div class="provider-name">No providers configured</div>
                    </div>
                    <p style="font-size: 12px; color: #6b7280; margin: 10px 0 0 0;">
                        Click "Add Provider" to configure an AI service for enhanced analysis.
                    </p>
                </div>
            `;
            return;
        }

        this.llmProviders.forEach(provider => {
            const providerElement = this.createProviderElement(provider);
            container.appendChild(providerElement);
        });

        console.log('✅ LLM providers rendered successfully');
    }

    createProviderElement(provider) {
        const element = document.createElement('div');
        element.className = `provider-item ${provider.isDefault ? 'active' : ''}`;
        element.innerHTML = `
            <div class="provider-header">
                <div class="provider-name">
                    ${this.getProviderIcon(provider.type || provider.name)} ${provider.name}
                </div>
                <span class="provider-status ${provider.apiKey ? 'connected' : 'disconnected'}">
                    ${provider.apiKey ? 'Connected' : 'Disconnected'}
                </span>
            </div>
            <div class="provider-actions">
                <button type="button" class="btn-icon" data-provider-id="${provider.id}" data-action="edit" title="Edit">
                    ✏️
                </button>
                <button type="button" class="btn-icon delete" data-provider-id="${provider.id}" data-action="delete" title="Delete">
                    🗑️
                </button>
            </div>
        `;

        // Add event listeners
        const buttons = element.querySelectorAll('.btn-icon');
        buttons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.dataset.action;
                const providerId = e.target.dataset.providerId;

                if (action === 'edit') {
                    this.editProvider(providerId);
                } else if (action === 'delete') {
                    this.deleteProvider(providerId);
                }
            });
        });

        return element;
    }

    getProviderIcon(type) {
        const icons = {
            openai: '🤖',
            anthropic: '🧠',
            gemini: '💎',
            openrouter: '🌐',
            ollama: '🏠',
            groq: '⚡',
            custom: '⚙️'
        };
        return icons[type] || '🔧';
    }

    showAddProviderModal() {
        console.log('🤖 Showing add provider modal...');

        // Check if modal exists in HTML
        const modal = document.getElementById('add-provider-modal');
        if (modal) {
            modal.classList.remove('hidden');
            this.selectedProviderTemplate = null;
            this.updateAddProviderForm();
        } else {
            // Fallback: use simple prompts
            this.showSimpleAddProvider();
        }
    }

    showSimpleAddProvider() {
        console.log('🤖 Using simple add provider dialog...');

        const name = prompt('Enter provider name (e.g., OpenAI, Gemini):');
        if (!name) return;

        const apiKey = prompt('Enter API key:');
        if (!apiKey) return;

        const model = prompt('Enter model name (e.g., gpt-4, gemini-pro):') || 'default';

        const provider = {
            id: Date.now().toString(),
            name: name,
            type: name.toLowerCase(),
            apiKey: apiKey,
            model: model,
            status: 'connected',
            isDefault: this.llmProviders.length === 0
        };

        this.llmProviders.push(provider);
        this.saveLLMProviders();
        this.renderLLMProviders();

        this.showNotification(`Provider ${name} added successfully!`, 'success');
    }

    hideAddProviderModal() {
        const modal = document.getElementById('add-provider-modal');
        if (modal) {
            modal.classList.add('hidden');
            this.resetAddProviderForm();
        }
    }

    selectProviderTemplate(providerType) {
        // Update template selection
        document.querySelectorAll('.provider-template').forEach(template => {
            template.classList.remove('selected');
        });

        const selectedTemplate = document.querySelector(`[data-provider="${providerType}"]`);
        if (selectedTemplate) {
            selectedTemplate.classList.add('selected');
        }

        this.selectedProviderTemplate = providerType;
        this.updateAddProviderForm();
    }

    updateAddProviderForm() {
        const form = document.getElementById('provider-form');
        const confirmBtn = document.getElementById('confirm-add-provider');

        if (!form || !confirmBtn) return;

        if (this.selectedProviderTemplate) {
            form.classList.remove('hidden');
            confirmBtn.disabled = false;

            // Pre-fill form based on template
            const templates = {
                openai: {
                    name: 'OpenAI',
                    baseUrl: 'https://api.openai.com/v1',
                    model: 'gpt-3.5-turbo',
                    maxTokens: 4000
                },
                anthropic: {
                    name: 'Anthropic',
                    baseUrl: 'https://api.anthropic.com/v1',
                    model: 'claude-3-sonnet-20240229',
                    maxTokens: 4000
                },
                gemini: {
                    name: 'Google Gemini',
                    baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
                    model: 'gemini-pro',
                    maxTokens: 4000
                },
                openrouter: {
                    name: 'OpenRouter',
                    baseUrl: 'https://openrouter.ai/api/v1',
                    model: 'openai/gpt-3.5-turbo',
                    maxTokens: 4000
                },
                ollama: {
                    name: 'Ollama',
                    baseUrl: 'http://localhost:11434/v1',
                    model: 'llama2',
                    maxTokens: 2000
                },
                groq: {
                    name: 'Groq',
                    baseUrl: 'https://api.groq.com/openai/v1',
                    model: 'llama2-70b-4096',
                    maxTokens: 4000
                },
                custom: {
                    name: 'Custom Provider',
                    baseUrl: '',
                    model: '',
                    maxTokens: 4000
                }
            };

            const template = templates[this.selectedProviderTemplate];
            if (template) {
                document.getElementById('provider-name').value = template.name;
                document.getElementById('provider-base-url').value = template.baseUrl;
                document.getElementById('provider-model').value = template.model;
                document.getElementById('provider-max-tokens').value = template.maxTokens;
            }
        } else {
            form.classList.add('hidden');
            confirmBtn.disabled = true;
        }
    }

    async addLLMProvider() {
        try {
            const newProvider = {
                id: 'provider-' + Date.now(),
                name: this.getElementValue('provider-name'),
                type: this.selectedProviderTemplate,
                apiKey: this.getElementValue('provider-api-key'),
                baseUrl: this.getElementValue('provider-base-url'),
                model: this.getElementValue('provider-model'),
                maxTokens: parseInt(this.getElementValue('provider-max-tokens')),
                enabled: true,
                isDefault: this.llmProviders.length === 0
            };

            this.llmProviders.push(newProvider);
            await this.saveLLMProviders();
            this.renderLLMProviders();
            this.hideAddProviderModal();

            this.showNotification(`${newProvider.name} provider added successfully!`, 'success');
        } catch (error) {
            console.error('Error adding provider:', error);
            this.showNotification('Error adding provider', 'error');
        }
    }

    async deleteProvider(providerId) {
        if (confirm('Are you sure you want to delete this provider?')) {
            try {
                this.llmProviders = this.llmProviders.filter(p => p.id !== providerId);
                await this.saveLLMProviders();
                this.renderLLMProviders();

                this.showNotification('Provider deleted successfully!', 'success');
            } catch (error) {
                console.error('Error deleting provider:', error);
                this.showNotification('Error deleting provider', 'error');
            }
        }
    }

    async updateProviderField(providerId, field, value) {
        try {
            const provider = this.llmProviders.find(p => p.id === providerId);
            if (provider) {
                provider[field] = value;
                await this.saveLLMProviders();

                // Update UI to show connection status
                setTimeout(() => {
                    this.renderLLMProviders();
                }, 100);
            }
        } catch (error) {
            console.error('Error updating provider:', error);
        }
    }

    editProvider(providerId) {
        console.log('✏️ Editing provider:', providerId);

        const provider = this.llmProviders.find(p => p.id === providerId);
        if (!provider) {
            console.error('Provider not found:', providerId);
            return;
        }

        // Simple edit using prompts
        const newApiKey = prompt('Enter new API key:', provider.apiKey || '');
        if (newApiKey !== null && newApiKey !== provider.apiKey) {
            provider.apiKey = newApiKey;
            this.saveLLMProviders();
            this.renderLLMProviders();
            this.showNotification('Provider updated successfully!', 'success');
        }
    }

    resetAddProviderForm() {
        document.getElementById('provider-name').value = '';
        document.getElementById('provider-api-key').value = '';
        document.getElementById('provider-base-url').value = '';
        document.getElementById('provider-model').value = '';
        document.getElementById('provider-max-tokens').value = '4000';

        document.querySelectorAll('.provider-template').forEach(template => {
            template.classList.remove('selected');
        });

        const form = document.getElementById('provider-form');
        if (form) {
            form.classList.add('hidden');
        }
    }

    // Mock data for testing outside extension context
    getMockPageData() {
        console.log('📄 Using mock page data for testing');

        const mockContent = `
        AI SEO Optimizer is a powerful Chrome extension for search engine optimization.
        This extension helps analyze web pages for SEO performance and provides actionable recommendations.
        Key features include keyword analysis, content optimization, technical SEO checks, and performance monitoring.
        The tool supports multiple languages and integrates with popular SEO APIs for comprehensive analysis.
        Users can generate detailed reports and export data for further analysis.
        Content marketing and digital marketing professionals find this tool invaluable for their workflow.
        Search engine ranking factors are constantly evolving, and this extension helps stay current with best practices.
        Meta tags, heading structure, image optimization, and content quality are all analyzed automatically.
        `;

        return {
            url: window.location.href || 'https://example.com/test-page',
            title: document.title || 'AI SEO Optimizer - Chrome Extension for SEO Analysis',
            metaDescription: document.querySelector('meta[name="description"]')?.content || 'Powerful Chrome extension for comprehensive SEO analysis, keyword research, and content optimization. Get actionable insights to improve your search engine rankings.',
            metaKeywords: 'seo, optimization, ai, chrome extension, keyword analysis, content marketing',
            headings: {
                h1: [{ text: 'AI SEO Optimizer - Complete SEO Analysis Tool' }],
                h2: [
                    { text: 'Advanced SEO Analysis Features' },
                    { text: 'Keyword Research and Optimization' },
                    { text: 'Content Quality Assessment' }
                ],
                h3: [
                    { text: 'Technical SEO Audit' },
                    { text: 'Performance Monitoring' },
                    { text: 'Competitive Analysis Tools' }
                ]
            },
            content: document.body ? document.body.innerText : mockContent.trim(),
            wordCount: document.body ? document.body.innerText.split(/\s+/).filter(w => w.length > 0).length : mockContent.split(/\s+/).filter(w => w.length > 0).length,
            images: [
                { src: 'seo-dashboard.jpg', alt: 'SEO dashboard showing analytics and performance metrics', title: 'SEO Dashboard', hasAlt: true },
                { src: 'keyword-analysis.jpg', alt: 'Keyword analysis tool interface', title: 'Keyword Tool', hasAlt: true },
                { src: 'missing-alt.jpg', alt: '', title: '', hasAlt: false }
            ],
            links: [
                { href: '/seo-guide', text: 'Complete SEO Guide', internal: true },
                { href: '/keyword-tools', text: 'Keyword Research Tools', internal: true },
                { href: 'https://developers.google.com/search', text: 'Google Search Central', internal: false },
                { href: 'https://moz.com/learn/seo', text: 'Moz SEO Learning Center', internal: false }
            ],
            timestamp: new Date().toISOString()
        };
    }

    getMockAnalysis() {
        return {
            seoScore: 78,
            keywordScore: 65,
            contentScore: 82,
            technicalScore: 74,
            recommendations: [
                {
                    type: 'title',
                    title: 'Optimize Title Tag',
                    description: 'Consider including more target keywords in your title tag'
                },
                {
                    type: 'meta',
                    title: 'Improve Meta Description',
                    description: 'Meta description could be more compelling and include target keywords'
                },
                {
                    type: 'image',
                    title: 'Add Alt Text to Images',
                    description: '1 image is missing alt text for better accessibility'
                },
                {
                    type: 'content',
                    title: 'Content Optimization',
                    description: 'Consider adding more relevant keywords throughout your content'
                },
                {
                    type: 'keyword',
                    title: 'Keyword Density',
                    description: 'Target keyword density is optimal at 2.1%'
                },
                {
                    type: 'technical',
                    title: 'Page Speed',
                    description: 'Consider optimizing images and minifying CSS/JS files'
                }
            ]
        };
    }

    // Utility methods
    getElementValue(id) {
        const element = document.getElementById(id);
        return element ? element.value : '';
    }

    setElementText(id, text) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = text;
        }
    }

    setElementHTML(id, html) {
        const element = document.getElementById(id);
        if (element) {
            element.innerHTML = html;
        }
    }

    showLoading(message = 'Loading...') {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            const text = overlay.querySelector('.loading-text');
            if (text) {
                text.textContent = message;
            }
            overlay.classList.remove('hidden');
        }
    }

    hideLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.add('hidden');
        }
    }

    showNotification(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        alert(message); // Simple notification for now
    }

    async runSpeedTest() {
        console.log('🚀 Running speed test...');
        this.showLoading('Running speed test...');

        try {
            // Check if we're in extension context
            if (typeof chrome !== 'undefined' && chrome.tabs && chrome.runtime) {
                const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

                if (!tab || !tab.url) {
                    throw new Error('Cannot access current tab');
                }

                // Use PageSpeed Insights API (requires API key)
                const response = await chrome.runtime.sendMessage({
                    action: 'runSpeedTest',
                    data: { url: tab.url }
                });

                if (response && response.success) {
                    this.showSpeedTestResults(response.data);
                } else {
                    throw new Error(response?.error || 'Speed test failed');
                }
            } else {
                // Fallback for testing
                this.showMockSpeedResults();
            }
        } catch (error) {
            console.error('Speed test error:', error);
            this.showNotification(`Speed test error: ${error.message}`, 'error');
            this.showMockSpeedResults();
        } finally {
            this.hideLoading();
        }
    }

    showSpeedTestResults(data) {
        const results = `
Speed Test Results:
• Performance Score: ${data.performance || 'N/A'}
• First Contentful Paint: ${data.fcp || 'N/A'}
• Largest Contentful Paint: ${data.lcp || 'N/A'}
• Cumulative Layout Shift: ${data.cls || 'N/A'}
• Time to Interactive: ${data.tti || 'N/A'}
        `;
        alert(results);
    }

    showMockSpeedResults() {
        const mockResults = `
Speed Test Results (Demo):
• Performance Score: 85/100
• First Contentful Paint: 1.2s
• Largest Contentful Paint: 2.1s
• Cumulative Layout Shift: 0.05
• Time to Interactive: 2.8s

Note: This is demo data. Connect to PageSpeed Insights API for real results.
        `;
        alert(mockResults);
    }

    async checkPageSpeed() {
        return this.runSpeedTest();
    }

    async exportReport() {
        console.log('Exporting report...');
        if (this.analysisData) {
            const report = {
                url: this.analysisData.url || 'Unknown',
                timestamp: new Date().toISOString(),
                scores: {
                    seo: this.analysisData.seoScore,
                    keywords: this.analysisData.keywordScore,
                    content: this.analysisData.contentScore,
                    technical: this.analysisData.technicalScore
                },
                recommendations: this.analysisData.recommendations || []
            };

            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `seo-report-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        } else {
            alert('No analysis data to export. Please analyze a page first.');
        }
    }

    async exportPDF() {
        console.log('📋 Exporting PDF...');
        alert('PDF export feature coming soon! Use JSON export for now.');
    }

    async clearData() {
        console.log('🗑️ Clearing data...');
        if (confirm('Are you sure you want to clear all stored data? This cannot be undone.')) {
            try {
                // Clear localStorage
                localStorage.clear();

                // Clear chrome storage if available
                if (typeof chrome !== 'undefined' && chrome.storage) {
                    await chrome.storage.local.clear();
                    await chrome.storage.sync.clear();
                }

                this.showNotification('All data cleared successfully!', 'success');

                // Reset UI
                this.analysisData = null;
                this.llmProviders = [];
                this.settings = {
                    targetKeywords: '',
                    keywordDensity: 2,
                    defaultProvider: ''
                };

                // Reload the popup
                window.location.reload();

            } catch (error) {
                console.error('Error clearing data:', error);
                this.showNotification(`Error clearing data: ${error.message}`, 'error');
            }
        }
    }

    showAddProviderModal() {
        console.log('🤖 Showing add provider modal...');
        // For now, use a simple prompt
        const name = prompt('Enter provider name (e.g., OpenAI, Gemini):');
        if (!name) return;

        const apiKey = prompt('Enter API key:');
        if (!apiKey) return;

        const model = prompt('Enter model name (e.g., gpt-4, gemini-pro):');
        if (!model) return;

        const provider = {
            id: Date.now().toString(),
            name: name,
            apiKey: apiKey,
            model: model,
            status: 'connected'
        };

        this.llmProviders.push(provider);
        this.saveLLMProviders();
        this.renderLLMProviders();

        this.showNotification(`Provider ${name} added successfully!`, 'success');
    }

    updateUI() {
        // Update UI based on current state
        console.log('UI updated');
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing popup...');
    new SimplePopup();
});

console.log('Popup script loaded');
