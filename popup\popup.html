<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI SEO Optimizer</title>
    <link rel="stylesheet" href="popup-modern.css">
    <style>
        /* Emergency CSS fix for tabs */
        .tab-content {
            display: none;
            padding: 20px;
            background: #f8fafc;
            min-height: 400px;
        }

        .tab-content.active {
            display: block !important;
        }

        /* Force show content */
        #analysis.active,
        #settings.active {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        /* Analysis styles */
        .analysis-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .analysis-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #e5e7eb;
        }

        .analysis-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .analysis-title {
            font-weight: 600;
            color: #374151;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-good { background: #dcfce7; color: #166534; }
        .status-medium { background: #fef3c7; color: #92400e; }
        .status-poor { background: #fee2e2; color: #991b1b; }

        .analysis-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .analysis-label {
            color: #6b7280;
            font-size: 14px;
        }

        .analysis-value {
            color: #374151;
            font-weight: 500;
            font-size: 14px;
        }

        /* Settings styles */
        .settings-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .settings-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e5e7eb;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #374151;
            font-size: 14px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-help {
            font-size: 12px;
            color: #6b7280;
            margin-top: 4px;
        }

        .provider-list {
            min-height: 100px;
            padding: 15px;
            background: #f9fafb;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <img src="../assets/icons/icon32.png" alt="AI SEO Optimizer" class="logo-icon">
                <h1>AI SEO Optimizer</h1>
            </div>
            <div class="nav-tabs">
                <button class="tab-btn active" data-tab="dashboard">Dashboard</button>
                <button class="tab-btn" data-tab="analysis">Analysis</button>
                <button class="tab-btn" data-tab="settings">Settings</button>
            </div>
        </header>

        <main class="main-content">
            <!-- Dashboard Tab -->
            <div id="dashboard" class="tab-content active">
                <div class="dashboard-grid">
                    <!-- SEO Score Section -->
                    <div class="seo-score-section">
                        <div class="score-header">
                            <div class="main-score" id="seo-score">--</div>
                            <div class="score-label">SEO Score</div>
                        </div>
                        <div class="score-breakdown">
                            <div class="score-item">
                                <div class="score-item-value good" id="keyword-score">--</div>
                                <div class="score-item-label">Keywords</div>
                            </div>
                            <div class="score-item">
                                <div class="score-item-value medium" id="content-score">--</div>
                                <div class="score-item-label">Content</div>
                            </div>
                            <div class="score-item">
                                <div class="score-item-value poor" id="technical-score">--</div>
                                <div class="score-item-label">Technical</div>
                            </div>
                            <div class="score-item">
                                <div class="score-item-value medium" id="overall-health">85</div>
                                <div class="score-item-label">Overall</div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Section -->
                    <div class="action-section">
                        <div class="section-title">
                            🚀 Quick Actions
                        </div>
                        <div class="action-buttons">
                            <button class="action-btn btn-primary" id="analyze-btn">
                                🔍 Analyze Page
                            </button>
                            <button class="action-btn btn-secondary" id="generate-keywords-btn">
                                💡 Keywords
                            </button>
                            <button class="action-btn btn-secondary" id="check-speed-btn">
                                ⚡ Speed Test
                            </button>
                            <button class="action-btn btn-secondary" id="export-report-btn">
                                📊 Export
                            </button>
                        </div>
                    </div>

                <div class="recent-analysis">
                    <h3>Recent Recommendations</h3>
                    <div id="recommendations-list" class="recommendations">
                        <div class="recommendation-item">
                            <div class="rec-icon">💡</div>
                            <div class="rec-content">
                                <div class="rec-title">Click "Analyze Current Page" to get started</div>
                                <div class="rec-description">Get AI-powered SEO recommendations for this page</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analysis Tab -->
            <div id="analysis" class="tab-content">
                <h2 style="color: #374151; margin-bottom: 20px;">📊 SEO Analysis Results</h2>
                <div class="analysis-container">
                    <!-- Meta Tags Analysis -->
                    <div class="analysis-card">
                        <div class="analysis-header">
                            <div class="analysis-title">📝 Meta Tags</div>
                            <div class="status-badge status-good" id="meta-status">Good</div>
                        </div>
                        <div class="analysis-content" id="meta-analysis">
                            <div class="analysis-item">
                                <span class="analysis-label">Title Length</span>
                                <span class="analysis-value">Click analyze to see results</span>
                            </div>
                            <div class="analysis-item">
                                <span class="analysis-label">Description</span>
                                <span class="analysis-value">Click analyze to see results</span>
                            </div>
                        </div>
                    </div>

                    <!-- Headings Analysis -->
                    <div class="analysis-card">
                        <div class="analysis-header">
                            <div class="analysis-title">📋 Headings</div>
                            <div class="status-badge status-medium" id="headings-status">Medium</div>
                        </div>
                        <div class="analysis-content" id="headings-analysis">
                            <div class="analysis-item">
                                <span class="analysis-label">H1 Tags</span>
                                <span class="analysis-value">Click analyze to see results</span>
                            </div>
                            <div class="analysis-item">
                                <span class="analysis-label">Structure</span>
                                <span class="analysis-value">Click analyze to see results</span>
                            </div>
                        </div>
                    </div>

                    <!-- Keywords Analysis -->
                    <div class="analysis-card">
                        <div class="analysis-header">
                            <div class="analysis-title">🎯 Keywords</div>
                            <div class="status-badge status-good" id="keywords-status">Good</div>
                        </div>
                        <div class="analysis-content" id="keyword-analysis">
                            <div class="analysis-item">
                                <span class="analysis-label">Density</span>
                                <span class="analysis-value">Click analyze to see results</span>
                            </div>
                            <div class="analysis-item">
                                <span class="analysis-label">Distribution</span>
                                <span class="analysis-value">Click analyze to see results</span>
                            </div>
                        </div>
                    </div>

                    <!-- Content Analysis -->
                    <div class="analysis-card">
                        <div class="analysis-header">
                            <div class="analysis-title">📄 Content</div>
                            <div class="status-badge status-medium" id="content-status">Medium</div>
                        </div>
                        <div class="analysis-content" id="content-analysis">
                            <div class="analysis-item">
                                <span class="analysis-label">Word Count</span>
                                <span class="analysis-value">Click analyze to see results</span>
                            </div>
                            <div class="analysis-item">
                                <span class="analysis-label">Readability</span>
                                <span class="analysis-value">Click analyze to see results</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div id="settings" class="tab-content">
                <h2 style="color: #374151; margin-bottom: 20px;">⚙️ Extension Settings</h2>
                <div class="settings-container">
                    <!-- API Settings -->
                    <div class="settings-card">
                        <div class="section-title">
                            🔑 SEO Configuration
                        </div>
                        <div class="form-group">
                            <label class="form-label">Target Keywords</label>
                            <input type="text" class="form-input" id="target-keywords" placeholder="seo, optimization, ai">
                            <div class="form-help">Comma-separated keywords to focus on</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Analysis Language</label>
                            <select class="form-input" id="analysis-language">
                                <option value="en">English</option>
                                <option value="ar">Arabic</option>
                                <option value="es">Spanish</option>
                                <option value="fr">French</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Keyword Density Target (%)</label>
                            <input type="range" class="form-input" id="keyword-density" min="1" max="5" value="2" step="0.5">
                            <div class="form-help">Current: <span id="density-value">2%</span></div>
                        </div>
                    </div>

                    <!-- LLM Providers -->
                    <div class="settings-card">
                        <div class="section-title">
                            🤖 LLM Providers
                            <button class="action-btn btn-primary" id="add-provider-btn" style="margin-left: auto; padding: 6px 12px; font-size: 11px;">
                                + Add Provider
                            </button>
                        </div>
                        <div class="provider-list" id="llm-providers-list">
                            <!-- Providers will be populated here -->
                        </div>
                    </div>

                    <!-- Export Settings -->
                    <div class="settings-card">
                        <div class="section-title">
                            📊 Tools & Export
                        </div>
                        <div class="action-buttons">
                            <button class="action-btn btn-secondary" id="export-json-btn">
                                📄 Export JSON
                            </button>
                            <button class="action-btn btn-secondary" id="export-pdf-btn">
                                📋 Export PDF
                            </button>
                            <button class="action-btn btn-primary" id="speed-test-btn">
                                ⚡ Speed Test
                            </button>
                            <button class="action-btn btn-secondary" id="clear-data-btn">
                                🗑️ Clear Data
                            </button>
                        </div>
                    </div>
                </div>
            </div>



            </div>
        </main>

    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">Loading...</div>
        </div>
    </div>

    <!-- Add Provider Modal -->
    <div id="add-provider-modal" class="modal-overlay hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add LLM Provider</h3>
                <button type="button" class="modal-close" id="close-modal">&times;</button>
            </div>

            <div class="provider-templates">
                <div class="provider-template" data-provider="openai">
                    <div class="template-name">🤖 OpenAI</div>
                    <div class="template-description">GPT-4, GPT-3.5 Turbo and other OpenAI models</div>
                    <div class="template-models">Models: gpt-4, gpt-3.5-turbo, gpt-4-turbo</div>
                </div>

                <div class="provider-template" data-provider="anthropic">
                    <div class="template-name">🧠 Anthropic</div>
                    <div class="template-description">Claude 3 Opus, Sonnet, and Haiku models</div>
                    <div class="template-models">Models: claude-3-opus, claude-3-sonnet, claude-3-haiku</div>
                </div>

                <div class="provider-template" data-provider="gemini">
                    <div class="template-name">💎 Google Gemini</div>
                    <div class="template-description">Google's advanced AI models including Gemini Pro</div>
                    <div class="template-models">Models: gemini-pro, gemini-pro-vision, gemini-1.5-pro</div>
                </div>

                <div class="provider-template" data-provider="openrouter">
                    <div class="template-name">🌐 OpenRouter</div>
                    <div class="template-description">Access to multiple AI models through one API</div>
                    <div class="template-models">Models: Various (GPT-4, Claude, Llama, etc.)</div>
                </div>

                <div class="provider-template" data-provider="ollama">
                    <div class="template-name">🏠 Ollama (Local)</div>
                    <div class="template-description">Run models locally on your machine</div>
                    <div class="template-models">Models: llama2, codellama, mistral, etc.</div>
                </div>

                <div class="provider-template" data-provider="groq">
                    <div class="template-name">⚡ Groq</div>
                    <div class="template-description">Ultra-fast inference with Groq LPU</div>
                    <div class="template-models">Models: llama2-70b, mixtral-8x7b, gemma-7b</div>
                </div>

                <div class="provider-template" data-provider="custom">
                    <div class="template-name">⚙️ Custom Provider</div>
                    <div class="template-description">Configure your own API endpoint</div>
                    <div class="template-models">Custom configuration required</div>
                </div>
            </div>

            <div id="provider-form" class="hidden">
                <div class="setting-group">
                    <label for="provider-name">Provider Name</label>
                    <input type="text" id="provider-name" placeholder="My Custom Provider">
                </div>

                <div class="setting-group">
                    <label for="provider-api-key">API Key</label>
                    <input type="password" id="provider-api-key" placeholder="Enter API key">
                </div>

                <div class="setting-group">
                    <label for="provider-base-url">Base URL</label>
                    <input type="url" id="provider-base-url" placeholder="https://api.example.com/v1">
                </div>

                <div class="setting-group">
                    <label for="provider-model">Default Model</label>
                    <input type="text" id="provider-model" placeholder="gpt-4">
                </div>

                <div class="setting-group">
                    <label for="provider-max-tokens">Max Tokens</label>
                    <input type="number" id="provider-max-tokens" value="4000" min="100" max="32000">
                </div>
            </div>

            <div class="modal-actions">
                <button type="button" id="cancel-add-provider" class="btn-secondary">Cancel</button>
                <button type="button" id="confirm-add-provider" class="btn-primary" disabled>Add Provider</button>
            </div>
        </div>
    </div>

    <script>
        // Emergency tab fix - inline JavaScript
        console.log('🚨 Emergency inline tab fix loading...');

        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚨 DOM loaded, setting up emergency tabs...');

            // Force setup tabs immediately
            setTimeout(() => {
                setupEmergencyTabs();
            }, 100);

            // Backup setup after delay
            setTimeout(() => {
                setupEmergencyTabs();
            }, 1000);
        });

        function setupEmergencyTabs() {
            console.log('🔧 Setting up emergency tabs...');

            const tabs = ['dashboard', 'analysis', 'settings'];

            // Setup tab buttons
            tabs.forEach(tabName => {
                const button = document.querySelector(`[data-tab="${tabName}"]`);
                const content = document.getElementById(tabName);

                console.log(`Tab ${tabName}: Button=${!!button}, Content=${!!content}`);

                if (button && content) {
                    // Remove existing listeners
                    button.onclick = null;

                    // Add new click handler
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('🖱️ Emergency tab clicked:', tabName);

                        // Hide all tabs
                        tabs.forEach(t => {
                            const el = document.getElementById(t);
                            if (el) {
                                el.style.display = 'none';
                                el.classList.remove('active');
                            }
                        });

                        // Show clicked tab
                        content.style.display = 'block';
                        content.classList.add('active');
                        console.log('✅ Tab shown:', tabName);

                        // Update button states
                        document.querySelectorAll('.tab-btn').forEach(btn => {
                            btn.classList.remove('active');
                        });
                        button.classList.add('active');
                    });
                }
            });

            // Show dashboard by default
            const dashboard = document.getElementById('dashboard');
            if (dashboard) {
                dashboard.style.display = 'block';
                dashboard.classList.add('active');
                console.log('✅ Dashboard shown by default');
            }

            console.log('✅ Emergency tabs setup completed');
        }
    </script>
    <script src="popup-simple.js"></script>
</body>
</html>
