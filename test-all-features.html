<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI SEO Optimizer - Complete Feature Test</title>
    <meta name="description" content="Comprehensive test page for AI SEO Optimizer Chrome extension. Test all features including analysis, settings, speed test, and keyword generation.">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            line-height: 1.6;
        }
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 380px 1fr;
            gap: 40px;
            align-items: flex-start;
        }
        .popup-container {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: sticky;
            top: 20px;
        }
        .content-area {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 30px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .test-section h3 {
            color: #fbbf24;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .test-steps {
            list-style: none;
            padding: 0;
        }
        .test-steps li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .test-steps li:last-child {
            border-bottom: none;
        }
        .step-number {
            background: #3b82f6;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            flex-shrink: 0;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        .feature-title {
            font-weight: 600;
            margin-bottom: 5px;
        }
        .feature-desc {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        .status-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            margin-left: 10px;
        }
        .status-working {
            background: #dcfce7;
            color: #166534;
        }
        .status-new {
            background: #dbeafe;
            color: #1e40af;
        }
        .status-fixed {
            background: #fef3c7;
            color: #92400e;
        }
        .highlight {
            background: rgba(255,255,255,0.2);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }
        .expected-results {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .expected-results h4 {
            color: #10b981;
            margin-bottom: 10px;
        }
        @media (max-width: 1200px) {
            .test-container {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            .popup-container {
                position: relative;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 AI SEO Optimizer</h1>
        <p>Complete Feature Testing Suite</p>
    </div>

    <div class="test-container">
        <div class="popup-container">
            <iframe src="popup/popup.html" width="380" height="600" frameborder="0"></iframe>
        </div>

        <div class="content-area">
            <div class="test-section">
                <h3>🧪 Testing Instructions</h3>
                <p>Follow these steps to test all features of the AI SEO Optimizer extension:</p>
                
                <ol class="test-steps">
                    <li>
                        <div class="step-number">1</div>
                        <span>Reload the extension in <span class="highlight">chrome://extensions/</span></span>
                    </li>
                    <li>
                        <div class="step-number">2</div>
                        <span>Click the extension icon to open the popup</span>
                    </li>
                    <li>
                        <div class="step-number">3</div>
                        <span>Test <span class="highlight">Dashboard</span> tab - click "🔍 Analyze Page"</span>
                    </li>
                    <li>
                        <div class="step-number">4</div>
                        <span>Test <span class="highlight">Analysis</span> tab - verify detailed results</span>
                    </li>
                    <li>
                        <div class="step-number">5</div>
                        <span>Test <span class="highlight">Settings</span> tab - try all buttons</span>
                    </li>
                </ol>
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <div class="feature-title">Dashboard</div>
                    <div class="feature-desc">SEO scores & recommendations</div>
                    <span class="status-indicator status-working">Working</span>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔍</div>
                    <div class="feature-title">Analysis</div>
                    <div class="feature-desc">Detailed page analysis</div>
                    <span class="status-indicator status-fixed">Fixed</span>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚙️</div>
                    <div class="feature-title">Settings</div>
                    <div class="feature-desc">Configuration & tools</div>
                    <span class="status-indicator status-fixed">Fixed</span>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <div class="feature-title">Speed Test</div>
                    <div class="feature-desc">Page performance analysis</div>
                    <span class="status-indicator status-new">New</span>
                </div>
            </div>

            <div class="test-section">
                <h3>📊 Dashboard Tab Tests</h3>
                <ol class="test-steps">
                    <li>
                        <div class="step-number">1</div>
                        <span>Click <span class="highlight">🔍 Analyze Page</span> button</span>
                    </li>
                    <li>
                        <div class="step-number">2</div>
                        <span>Verify SEO scores appear (should show real numbers)</span>
                    </li>
                    <li>
                        <div class="step-number">3</div>
                        <span>Check recommendations list is populated</span>
                    </li>
                    <li>
                        <div class="step-number">4</div>
                        <span>Try other buttons: Keywords, Speed Test, Export</span>
                    </li>
                </ol>
                
                <div class="expected-results">
                    <h4>✅ Expected Results:</h4>
                    <ul>
                        <li>No JavaScript errors in console</li>
                        <li>SEO score displays (e.g., 78/100)</li>
                        <li>Individual scores show with colors</li>
                        <li>Recommendations appear with icons</li>
                    </ul>
                </div>
            </div>

            <div class="test-section">
                <h3>🔍 Analysis Tab Tests</h3>
                <ol class="test-steps">
                    <li>
                        <div class="step-number">1</div>
                        <span>Switch to <span class="highlight">Analysis</span> tab</span>
                    </li>
                    <li>
                        <div class="step-number">2</div>
                        <span>Run analysis from Dashboard first</span>
                    </li>
                    <li>
                        <div class="step-number">3</div>
                        <span>Check all 4 analysis cards show data</span>
                    </li>
                    <li>
                        <div class="step-number">4</div>
                        <span>Verify status badges show correct colors</span>
                    </li>
                </ol>
                
                <div class="expected-results">
                    <h4>✅ Expected Results:</h4>
                    <ul>
                        <li>Meta Tags analysis shows title/description info</li>
                        <li>Headings analysis shows H1/H2/H3 counts</li>
                        <li>Keywords analysis shows density info</li>
                        <li>Content analysis shows word count/readability</li>
                    </ul>
                </div>
            </div>

            <div class="test-section">
                <h3>⚙️ Settings Tab Tests</h3>
                <ol class="test-steps">
                    <li>
                        <div class="step-number">1</div>
                        <span>Switch to <span class="highlight">Settings</span> tab</span>
                    </li>
                    <li>
                        <div class="step-number">2</div>
                        <span>Try adding target keywords</span>
                    </li>
                    <li>
                        <div class="step-number">3</div>
                        <span>Click <span class="highlight">+ Add Provider</span> button</span>
                    </li>
                    <li>
                        <div class="step-number">4</div>
                        <span>Test <span class="highlight">⚡ Speed Test</span> button</span>
                    </li>
                    <li>
                        <div class="step-number">5</div>
                        <span>Try <span class="highlight">📄 Export JSON</span> button</span>
                    </li>
                    <li>
                        <div class="step-number">6</div>
                        <span>Test <span class="highlight">🗑️ Clear Data</span> button</span>
                    </li>
                </ol>
                
                <div class="expected-results">
                    <h4>✅ Expected Results:</h4>
                    <ul>
                        <li>Keywords field accepts input</li>
                        <li>Add Provider shows prompts for name/key/model</li>
                        <li>Speed Test shows loading then results</li>
                        <li>Export downloads JSON file</li>
                        <li>Clear Data asks for confirmation</li>
                    </ul>
                </div>
            </div>

            <div class="test-section">
                <h3>⚡ Speed Test Feature</h3>
                <p>New feature that analyzes page performance:</p>
                <ol class="test-steps">
                    <li>
                        <div class="step-number">1</div>
                        <span>Go to Settings tab</span>
                    </li>
                    <li>
                        <div class="step-number">2</div>
                        <span>Click <span class="highlight">⚡ Speed Test</span> button</span>
                    </li>
                    <li>
                        <div class="step-number">3</div>
                        <span>Wait for loading to complete</span>
                    </li>
                    <li>
                        <div class="step-number">4</div>
                        <span>Review performance metrics</span>
                    </li>
                </ol>
                
                <div class="expected-results">
                    <h4>✅ Expected Results:</h4>
                    <ul>
                        <li>Loading overlay appears</li>
                        <li>Results show performance score (70-100)</li>
                        <li>Core Web Vitals metrics displayed</li>
                        <li>No errors in console</li>
                    </ul>
                </div>
            </div>

            <div class="test-section">
                <h3>🐛 Known Issues Fixed</h3>
                <ul class="test-steps">
                    <li>
                        <div class="step-number">✅</div>
                        <span>Analysis tab now shows detailed results</span>
                    </li>
                    <li>
                        <div class="step-number">✅</div>
                        <span>Settings tab completely redesigned</span>
                    </li>
                    <li>
                        <div class="step-number">✅</div>
                        <span>Speed test feature added</span>
                    </li>
                    <li>
                        <div class="step-number">✅</div>
                        <span>All buttons now have proper event handlers</span>
                    </li>
                    <li>
                        <div class="step-number">✅</div>
                        <span>CSS classes match between HTML and CSS</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        console.log('🧪 Complete Feature Test Page Loaded');
        
        // Log test information
        console.log('📊 Test Features:');
        console.log('- Dashboard: SEO analysis and recommendations');
        console.log('- Analysis: Detailed breakdown of SEO factors');
        console.log('- Settings: Configuration and tools');
        console.log('- Speed Test: Page performance analysis');
        console.log('- Export: JSON report generation');
        console.log('- LLM Providers: AI service management');
        
        // Check if popup loads correctly
        const iframe = document.querySelector('iframe');
        iframe.onload = function() {
            console.log('✅ Popup loaded successfully');
            console.log('📏 Dimensions: 380x600px');
        };
        
        iframe.onerror = function() {
            console.error('❌ Failed to load popup');
        };

        // Add some interactive effects
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.transition = 'transform 0.3s ease';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
