<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test - AI SEO Optimizer</title>
    <meta name="description" content="Simple test page for AI SEO Optimizer extension to verify tab functionality">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            line-height: 1.6;
            background: #f0f2f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2563eb;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-box {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .instruction {
            background: #dbeafe;
            border-left: 4px solid #3b82f6;
            padding: 15px;
            margin: 15px 0;
        }
        .success {
            background: #dcfce7;
            border-left: 4px solid #16a34a;
            padding: 15px;
            margin: 15px 0;
        }
        .warning {
            background: #fef3c7;
            border-left: 4px solid #d97706;
            padding: 15px;
            margin: 15px 0;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e5e7eb;
        }
        .step-number {
            background: #3b82f6;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار بسيط للإضافة</h1>
        
        <div class="instruction">
            <strong>📋 التعليمات:</strong><br>
            هذه صفحة اختبار بسيطة للتحقق من عمل إضافة AI SEO Optimizer بعد الإصلاحات الجديدة.
        </div>

        <div class="test-box">
            <h2>🚀 خطوات الاختبار</h2>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>أعد تحميل الإضافة:</strong><br>
                اذهب إلى chrome://extensions/ وانقر "Reload" على إضافة AI SEO Optimizer
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>افتح الإضافة:</strong><br>
                انقر على أيقونة الإضافة في شريط الأدوات
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>اختبر التبويبات:</strong><br>
                انقر على كل تبويب وتحقق من ظهور المحتوى:
                <ul>
                    <li><strong>Dashboard:</strong> يجب أن ترى SEO Score والأزرار</li>
                    <li><strong>Analysis:</strong> يجب أن ترى "📊 SEO Analysis Results"</li>
                    <li><strong>Settings:</strong> يجب أن ترى "⚙️ Extension Settings"</li>
                </ul>
            </div>
        </div>

        <div class="success">
            <strong>✅ علامات النجاح:</strong><br>
            • جميع التبويبات تظهر محتواها<br>
            • التبديل بين التبويبات يعمل بسلاسة<br>
            • ترى العناوين الجديدة في Analysis و Settings<br>
            • لا توجد مساحات بيضاء فارغة
        </div>

        <div class="warning">
            <strong>⚠️ إذا لم تعمل:</strong><br>
            1. افتح Developer Tools (F12)<br>
            2. اذهب إلى Console<br>
            3. ابحث عن رسائل تبدأ بـ 🚨 Emergency<br>
            4. إذا لم تجد الرسائل، أعد تحميل الإضافة مرة أخرى
        </div>

        <div class="test-box">
            <h2>🔧 حل سريع</h2>
            <p>إذا استمرت المشكلة، افتح Console (F12) واكتب:</p>
            <code style="background: #f1f5f9; padding: 10px; display: block; border-radius: 5px; margin: 10px 0;">
                document.getElementById('analysis').style.display = 'block';<br>
                document.getElementById('settings').style.display = 'block';
            </code>
        </div>

        <div class="instruction">
            <strong>📝 ملاحظة:</strong><br>
            تم إضافة CSS و JavaScript مباشر في ملف popup.html لضمان عمل التبويبات. 
            يجب أن تعمل الآن بشكل صحيح!
        </div>
    </div>

    <script>
        console.log('🧪 Simple test page loaded');
        console.log('📄 Page ready for extension testing');
        
        // Add some content for SEO analysis
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ Test page fully loaded');
            console.log('🎯 Ready for AI SEO Optimizer analysis');
        });
    </script>
</body>
</html>
