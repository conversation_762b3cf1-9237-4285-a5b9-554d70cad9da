<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Tabs - AI SEO Optimizer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-steps {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .step {
            margin: 5px 0;
            padding: 5px 0;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #007bff;
            font-weight: bold;
        }
        code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار تبويبات AI SEO Optimizer</h1>
        
        <div class="test-section">
            <div class="test-title">📋 خطوات الاختبار</div>
            <div class="test-steps">
                <div class="step">1. تأكد من تحميل الإضافة في Chrome</div>
                <div class="step">2. انقر على أيقونة الإضافة</div>
                <div class="step">3. يجب أن تفتح النافذة المنبثقة</div>
                <div class="step">4. جرب النقر على كل تبويب:</div>
                <div class="step" style="margin-left: 20px;">• Dashboard (يجب أن يظهر)</div>
                <div class="step" style="margin-left: 20px;">• Analysis (يجب أن يظهر)</div>
                <div class="step" style="margin-left: 20px;">• Settings (يجب أن يظهر)</div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔍 ما يجب أن تراه</div>
            <div class="test-steps">
                <div class="step"><span class="success">✅ Dashboard Tab:</span></div>
                <div class="step" style="margin-left: 20px;">• SEO Score (--)</div>
                <div class="step" style="margin-left: 20px;">• Quick Actions buttons</div>
                <div class="step" style="margin-left: 20px;">• Recent Recommendations</div>
                
                <div class="step"><span class="success">✅ Analysis Tab:</span></div>
                <div class="step" style="margin-left: 20px;">• Meta Tags Analysis</div>
                <div class="step" style="margin-left: 20px;">• Headings Analysis</div>
                <div class="step" style="margin-left: 20px;">• Keywords Analysis</div>
                <div class="step" style="margin-left: 20px;">• Content Analysis</div>
                
                <div class="step"><span class="success">✅ Settings Tab:</span></div>
                <div class="step" style="margin-left: 20px;">• SEO Configuration</div>
                <div class="step" style="margin-left: 20px;">• LLM Providers section</div>
                <div class="step" style="margin-left: 20px;">• Tools & Export section</div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🚨 استكشاف الأخطاء</div>
            <div class="test-steps">
                <div class="step"><span class="error">❌ إذا لم تظهر التبويبات:</span></div>
                <div class="step">1. افتح Developer Tools (F12)</div>
                <div class="step">2. اذهب إلى Console</div>
                <div class="step">3. ابحث عن رسائل مثل:</div>
                <div class="step" style="margin-left: 20px;"><code>🔄 Switching to tab: analysis</code></div>
                <div class="step" style="margin-left: 20px;"><code>✅ Tab content activated: analysis</code></div>
                
                <div class="step"><span class="error">❌ إذا ظهرت أخطاء:</span></div>
                <div class="step">1. تحقق من رسائل الخطأ في Console</div>
                <div class="step">2. أعد تحميل الإضافة</div>
                <div class="step">3. جرب إغلاق وإعادة فتح النافذة المنبثقة</div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔧 إصلاحات إضافية</div>
            <div class="test-steps">
                <div class="step"><span class="info">💡 إذا استمرت المشكلة:</span></div>
                <div class="step">1. تأكد من أن ملف <code>popup-modern.css</code> محمل</div>
                <div class="step">2. تحقق من أن <code>popup-simple.js</code> يعمل</div>
                <div class="step">3. في Console، اكتب: <code>document.querySelectorAll('.tab-content')</code></div>
                <div class="step">4. يجب أن ترى 3 عناصر</div>
                
                <div class="step"><span class="info">💡 اختبار يدوي:</span></div>
                <div class="step">في Console، جرب:</div>
                <div class="step"><code>document.getElementById('analysis').style.display = 'block'</code></div>
                <div class="step"><code>document.getElementById('settings').style.display = 'block'</code></div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">📊 نتائج الاختبار</div>
            <div class="test-steps">
                <div class="step"><span class="success">✅ نجح الاختبار إذا:</span></div>
                <div class="step">• جميع التبويبات الثلاثة تظهر محتواها</div>
                <div class="step">• التبديل بين التبويبات يعمل بسلاسة</div>
                <div class="step">• لا توجد أخطاء في Console</div>
                <div class="step">• المحتوى يظهر ويختفي بشكل صحيح</div>
                
                <div class="step"><span class="error">❌ فشل الاختبار إذا:</span></div>
                <div class="step">• تبويب Analysis أو Settings لا يظهر محتوى</div>
                <div class="step">• توجد أخطاء في Console</div>
                <div class="step">• التبديل بين التبويبات لا يعمل</div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🎯 اختبار سريع</div>
            <div class="test-steps">
                <div class="step">1. افتح الإضافة</div>
                <div class="step">2. انقر Analysis → يجب أن ترى "Meta Tags Analysis"</div>
                <div class="step">3. انقر Settings → يجب أن ترى "SEO Configuration"</div>
                <div class="step">4. انقر Dashboard → يجب أن ترى "SEO Score"</div>
            </div>
        </div>
    </div>

    <script>
        console.log('🧪 Test page loaded for tab testing');
        
        // Add some test content
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 Test page ready');
            
            // Log instructions
            console.log('📋 Instructions:');
            console.log('1. Open the extension popup');
            console.log('2. Try clicking each tab');
            console.log('3. Check if content appears');
            console.log('4. Look for console messages starting with 🔄 or ✅');
        });
    </script>
</body>
</html>
